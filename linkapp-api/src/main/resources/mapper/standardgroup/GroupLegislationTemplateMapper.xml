<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.standardgroup.mapper.GroupLegislationTemplateMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.standardgroup.entity.GroupLegislationTemplate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="item_type" property="itemType" jdbcType="VARCHAR"/>
        <result column="item_content" property="itemContent" jdbcType="VARCHAR"/>
        <result column="score_unit" property="scoreUnit" jdbcType="VARCHAR"/>
        <result column="score_value" property="scoreValue" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="delete_state" property="deleteState" jdbcType="INTEGER"/>
        <result column="create_time_" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_id_" property="createId" jdbcType="BIGINT"/>
        <result column="modify_time_" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="modify_id_" property="modifyId" jdbcType="BIGINT"/>
        <result column="remark_" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, item_type, item_content, score_unit, score_value, sort_order, delete_state,
        create_time_, create_id_, modify_time_, modify_id_, remark_
    </sql>

    <!-- 查询启用的考核项模板列表 -->
    <select id="selectActiveTemplates" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_legislation_template
        WHERE delete_state = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据类型查询考核项模板列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_legislation_template
        WHERE delete_state = 0
        <if test="itemType != null and itemType != ''">
            AND item_type = #{itemType}
        </if>
        ORDER BY sort_order ASC, id ASC
    </select>

</mapper> 