<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.standardgroup.mapper.GroupDailyAssessmentItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.standardgroup.vo.GroupDailyAssessmentItemVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="assessment_id" property="assessmentId" jdbcType="BIGINT"/>
        <result column="item_type" property="itemType" jdbcType="VARCHAR"/>
        <result column="item_content" property="itemContent" jdbcType="VARCHAR"/>
        <result column="score_unit" property="scoreUnit" jdbcType="VARCHAR"/>
        <result column="score_value" property="scoreValue" jdbcType="VARCHAR"/>
        <result column="deduct_score" property="deductScore" jdbcType="DECIMAL"/>
        <result column="bonus_score" property="bonusScore" jdbcType="DECIMAL"/>
        <result column="is_veto" property="isVeto" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, assessment_id, item_type, item_content, score_unit, score_value, deduct_score, bonus_score, is_veto, sort_order
    </sql>

    <!-- 根据考核记录ID查询考核项列表 -->
    <select id="selectByAssessmentId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment_item
        WHERE assessment_id = #{assessmentId} AND delete_state = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据考核记录ID删除考核项 -->
    <delete id="deleteByAssessmentId">
        DELETE FROM rail_group_daily_assessment_item WHERE assessment_id = #{assessmentId}
    </delete>

    <!-- 批量插入考核项 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO rail_group_daily_assessment_item (
            assessment_id, item_type, item_content, score_unit, score_value, deduct_score, bonus_score, 
            is_veto, sort_order, tenant_id, delete_state, create_id_, create_time_, modify_id_, modify_time_
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.assessmentId}, #{item.itemType}, #{item.itemContent}, #{item.scoreUnit}, #{item.scoreValue},
                #{item.deductScore}, #{item.bonusScore}, #{item.isVeto}, #{item.sortOrder},
                #{item.tenantId}, #{item.deleteState}, #{item.createId}, #{item.createTime},
                #{item.modifyId}, #{item.modifyTime}
            )
        </foreach>
    </insert>

</mapper> 