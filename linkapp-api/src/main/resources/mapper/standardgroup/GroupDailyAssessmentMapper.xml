<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.standardgroup.mapper.GroupDailyAssessmentMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.standardgroup.vo.GroupDailyAssessmentVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="assessment_date" property="assessmentDate" jdbcType="DATE"/>
        <result column="total_score" property="totalScore" jdbcType="DECIMAL"/>
        <result column="assessment_result" property="assessmentResult" jdbcType="VARCHAR"/>
        <result column="supervisor_signature" property="supervisorSignature" jdbcType="VARCHAR"/>
        <result column="assessed_person_ids" property="assessedPersonIds" jdbcType="LONGVARCHAR" 
                typeHandler="com.easylinkin.linkappapi.common.typehandler.LongListHandler"/>
        <result column="assessed_person_names" property="assessedPersonNames" jdbcType="LONGVARCHAR" 
                typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
        <result column="create_time_" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP"/>
        <result column="uploader_id" property="uploaderId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        a.id, a.project_name, a.assessment_date, a.total_score, 
        a.assessment_result, a.supervisor_signature, a.assessed_person_ids, 
        a.assessed_person_names, a.create_time_, a.uploader_id,
        a.upload_time, u.real_name as create_name
    </sql>

    <!-- 基础查询条件 -->
    <sql id="Base_Where_Clause">
        WHERE a.delete_state = 0
        <if test="tenantId != null and tenantId != ''">
            AND a.tenant_id = #{tenantId}
        </if>
        <if test="assessmentDate != null">
            AND DATE(a.assessment_date) = DATE(#{assessmentDate})
        </if>
    </sql>

    <!-- 分页查询班组日考核列表 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment a
        LEFT JOIN rail_linkapp_roster_personnel u ON a.uploader_id = u.id
        <include refid="Base_Where_Clause"/>
        ORDER BY a.create_time_ DESC
    </select>

    <!-- 分页查询班组日考核列表（带查询条件） -->
    <select id="selectPageWithQuery" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment a
        LEFT JOIN rail_linkapp_roster_personnel u ON a.uploader_id = u.id
        WHERE a.delete_state = 0
        <if test="requestModel.tenantId != null and requestModel.tenantId != ''">
            AND a.tenant_id = #{requestModel.tenantId}
        </if>
        <if test="requestModel.assessmentMonth != null and requestModel.assessmentMonth != ''">
            AND DATE_FORMAT(a.assessment_date, '%Y%m') = #{requestModel.assessmentMonth}
        </if>
        <if test="requestModel.uploaderName != null and requestModel.uploaderName != ''">
            AND u.real_name LIKE CONCAT('%', #{requestModel.uploaderName}, '%')
        </if>
        <choose>
            <when test="requestModel.sortField != null and requestModel.sortField != '' and requestModel.sortOrder != null and requestModel.sortOrder != ''">
                ORDER BY 
                <choose>
                    <when test="requestModel.sortField == 'assessmentDate'">
                        a.assessment_date
                    </when>
                    <when test="requestModel.sortField == 'totalScore'">
                        a.total_score
                    </when>
                    <otherwise>
                        a.create_time_
                    </otherwise>
                </choose>
                <choose>
                    <when test="requestModel.sortOrder == 'asc'">
                        ASC
                    </when>
                    <when test="requestModel.sortOrder == 'desc'">
                        DESC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY a.create_time_ DESC
            </otherwise>
        </choose>
    </select>
    
    <!-- 根据日期查询考核记录 -->
    <select id="selectByDate" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment a
        LEFT JOIN rail_linkapp_roster_personnel u ON a.create_id_ = u.id
        <include refid="Base_Where_Clause"/>
        LIMIT 1
    </select>

    <!-- 查询班组日考核详情 -->
    <select id="selectDetailById" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment a
        LEFT JOIN rail_linkapp_roster_personnel u ON a.create_id_ = u.id
        WHERE a.id = #{id} AND a.delete_state = 0
    </select>

    <!-- 查询有考核数据的月份列表 -->
    <select id="selectAssessmentMonths" resultType="string">
        SELECT DISTINCT DATE_FORMAT(assessment_date, '%Y%m') as month
        FROM rail_group_daily_assessment
        WHERE delete_state = 0
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY month DESC
    </select>

    <!-- 根据ID列表查询考核记录 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM rail_group_daily_assessment a
        LEFT JOIN rail_linkapp_roster_personnel u ON a.create_id_ = u.id
        WHERE a.delete_state = 0
<!--        <if test="tenantId != null and tenantId != ''">-->
<!--            AND a.tenant_id = #{tenantId}-->
<!--        </if>-->
        AND a.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY a.assessment_date DESC
    </select>

</mapper> 