<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.standardgroup.mapper.StandGroupAcceptanceFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.standardgroup.entity.StandGroupAcceptanceFile">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="file_type" property="fileType"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="create_uid" property="createUid"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, file_type, file_url, file_name, create_uid, create_time
    </sql>

    <!-- 查询文件列表 -->
    <select id="selectList" resultType="com.easylinkin.linkappapi.standardgroup.vo.StandGroupAcceptanceFileVo">
        SELECT 
            f.id,
            f.tenant_id,
            f.file_type,
            f.file_url,
            f.file_name,
            f.create_uid,
            f.create_time,
            lu.real_name as createUserName,
            lt.platform_project_name as tenantName
        FROM rail_stand_group_file f
        left join rail_linkapp_roster_personnel lu on f.create_uid = lu.id
        left join linkapp_tenant lt on f.tenant_id = lt.id
        <where>
            <if test="standGroupAcceptanceFileDto.tenantId != null and standGroupAcceptanceFileDto.tenantId != ''">
                AND f.tenant_id = #{standGroupAcceptanceFileDto.tenantId}
            </if>
            <if test="standGroupAcceptanceFileDto.fileType != null">
                AND f.file_type = #{standGroupAcceptanceFileDto.fileType}
            </if>
            <if test="standGroupAcceptanceFileDto.fileName != null and standGroupAcceptanceFileDto.fileName != ''">
                AND f.file_name LIKE CONCAT('%', #{standGroupAcceptanceFileDto.fileName}, '%')
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

</mapper> 