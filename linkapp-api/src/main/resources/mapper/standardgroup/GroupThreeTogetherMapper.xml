<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.standardgroup.mapper.GroupThreeTogetherMapper">

    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.standardgroup.entity.GroupThreeTogether">
        <id property="id" column="id" />
        <result property="type" column="type" />
        <result property="threeTogetherTime" column="three_together_time" />
        <result property="imgList" column="img_list" typeHandler="com.easylinkin.linkappapi.common.typehandler.StringToListHandler"/>
        <result property="remark" column="remark" />
        <result property="uploaderId" column="uploader_id" />
        <result property="tenantId" column="tenant_id" />
        <result property="deleteState" column="delete_state" />
        <result property="createTime" column="create_time_" />
        <result property="createId" column="create_id_" />
        <result property="modifyTime" column="modify_time_" />
        <result property="modifyId" column="create_id_" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        type,
        three_together_time,
        img_list,
        remark,
        uploader_id,
        tenant_id,
        delete_state,
        create_time_,
        create_id_,
        modify_time_,
        modify_id_
    </sql>




    <!-- 查询指定日期范围内每日是否有记录 -->
    <select id="queryDailyStatus" resultType="com.easylinkin.linkappapi.standardgroup.mapper.GroupThreeTogetherMapper$DailyStatusResult">
        SELECT 
            days.day as day,
            CASE WHEN COUNT(t.id) > 0 THEN 1 ELSE 0 END as status
        FROM (
            SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
            SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
            SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
            SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
            SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
            SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION SELECT 31
        ) as days
        LEFT JOIN rail_group_three_together t ON DAY(t.three_together_time) = days.day
            AND YEAR(t.three_together_time) = YEAR(#{startDate})
            AND MONTH(t.three_together_time) = MONTH(#{startDate})
            AND t.tenant_id = #{tenantId}
            AND t.delete_state = 0
        WHERE days.day BETWEEN 1 AND DAY(LAST_DAY(#{startDate}))
        GROUP BY days.day
        ORDER BY days.day
    </select>

    <!-- 查询指定日期的记录列表 -->
    <select id="queryByDate" resultType="com.easylinkin.linkappapi.standardgroup.entity.GroupThreeTogether">
        SELECT 
            <include refid="Base_Column_List" />
        FROM rail_group_three_together
        WHERE DATE(three_together_time) = #{queryDate}
            AND tenant_id = #{tenantId}
            AND delete_state = 0
        ORDER BY type, three_together_time DESC
    </select>

    <!-- 分页查询班组三同时记录（标准格式） -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT 
          <include refid="Base_Column_List" />
        FROM rail_group_three_together
        WHERE tenant_id = #{queryDTO.tenantId}
        AND delete_state = 0
            <if test="queryDTO.type != null">
                AND type = #{queryDTO.type}
            </if>
            <if test="queryDTO.startDate != null">
                AND DATE(three_together_time) <![CDATA[>=]]> #{queryDTO.startDate}
            </if>
            <if test="queryDTO.endDate != null">
                AND DATE(three_together_time) <![CDATA[<=]]> #{queryDTO.endDate}
            </if>
        ORDER BY three_together_time DESC
    </select>

</mapper> 