-- 合格班组授牌记录表
CREATE TABLE `rail_qualified_team_record` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `img_path_` varchar(500) DEFAULT NULL COMMENT '图片路径',
  `remark_` varchar(1000) DEFAULT NULL COMMENT '备注',
  `tenant_id_` varchar(50) DEFAULT NULL COMMENT '租户ID',
  `create_time_` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id_`),
  KEY `idx_tenant_id` (`tenant_id_`),
  KEY `idx_create_time` (`create_time_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合格班组授牌记录表'; 