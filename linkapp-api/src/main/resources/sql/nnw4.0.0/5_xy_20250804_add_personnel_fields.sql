-- 为班组日考核表添加人员字段

-- 添加被考核人员ID列表字段
ALTER TABLE `rail_group_daily_assessment` 
ADD COLUMN `assessed_person_ids` text COMMENT '被考核人员ID列表（JSON格式存储）' AFTER `supervisor_signature`;

-- 添加被考核人员姓名列表字段
ALTER TABLE `rail_group_daily_assessment` 
ADD COLUMN `assessed_person_names` text COMMENT '被考核人员姓名列表（JSON格式存储）' AFTER `assessed_person_ids`;

-- 新增上传人id字段
ALTER TABLE `rail_group_daily_assessment`
    ADD COLUMN  `uploader_id` bigint(20) not null COMMENT '上传人ID' AFTER `assessed_person_names`;
-- 新增上传时间
ALTER TABLE `rail_group_daily_assessment`
    ADD COLUMN  `upload_time` datetime not null COMMENT '上传时间' AFTER `uploader_id`;