CREATE TABLE `rail_linkapp_high_formwork_item` (
  `id` varchar(32) NOT NULL,
  `item_name` varchar(255) DEFAULT NULL COMMENT '分项名称',
  `high_code` varchar(32) DEFAULT NULL COMMENT '绑定高支模唯一标识',
  `create_time` datetime DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `start_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `end_time` datetime DEFAULT NULL COMMENT '解绑时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='高支模分项记录表';

