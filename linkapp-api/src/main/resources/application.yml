server:
  port: 5042
  servlet:
    session:
      # 单位:秒
      timeout: 86400

spring:
  resources:
    static-locations: classpath:static/
  task:
    execution:
      pool:
        core-size: 30
        max-size: 100
  profiles:
    active: nnw-dev
  cache:
    type: redis
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    sql-script-encoding: utf-8
    publicKey:
    #  dev 数据库配置
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url:
      username:
      password:
      initial-size: 20
      max-active: 100
      min-idle: 20
      max-wait: 60000
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: stat
      password-callback: com.easylinkin.linkappapi.common.utils.DbPasswordCallback

      connection-properties: config.decrypt=true;publickey=${spring.datasource.publicKey};password=${spring.datasource.druid.password}
      filter:
        config:
          enabled: true
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
      validationQuery: SELECT 1 FROM DUAL
# quartz 配置开始


  quartz:
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: DefaultQuartzScheduler
            instanceId: AUTO
          jobStore:
            #使用自己的配置文件
            useProperties: false
            #存储方式使用JobStoreTX，也就是数据库
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            #数据库中quartz表的表名前缀
            tablePrefix: QRTZ_
            #是否使用集群（如果项目只部署到 一台服务器，就不用了）
            isClustered: true
            clusterCheckinInterval: 20000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    #数据库方式
    job-store-type: jdbc
    #初始化表结构
    jdbc:
      initialize-schema: never



  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  #    为null 则不返回
#    default-property-inclusion: non_null
  messages:
    #basename: morn/message,i18n/message
    basename: i18n/message,morn/operation
    encoding: UTF-8

  jpa:
    database-platform: org.hibernate.dialect.MySQL5Dialect
    properties:
      hibernate:
        format_sql: true
  #        hbm2ddl:
  #          auto: update

  redis:
    database: 0
    host:
    port: 6379
    password:
    timeout: 3000
    pool:
      max-active: 200
      max-wait: -1
      max-idle: 10
      min-idle: 0

  main:
    allow-bean-definition-overriding: true

logging:
  config: classpath:conf/logback-spring.xml
  file:
    path: ./elk/log/java/linkapp-backend_4_${spring.profiles.active}

mybatis-plus:
  global-config:
    db-config:
      id-type: uuid
      field-strategy: not_empty
      #驼峰下划线转换
      table-underline: true
      #      db-type: mysql
      logic-delete-value: 0 #逻辑已删除
      logic-not-delete-value: 1 #逻辑未删除
  sql-injector: com.baomidou.mybatisplus.extension.injector.LogicSqlInjector
  mapper-locations: classpath:/mapper/**/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

morn:
  translator:
    enabled: true
  exception-aspect:
    enabled: true
  exception-interpreter:
    enabled: true
    validate:
      enabled: true
  operate:
    enabled: true
    default-converter:
      enabled: true

es:
  isUseOnlineEs: false
  port: 9200
  schema: http
  connectNum: 1
  connectPerRoute: 10

linkapp:
  url:

linkthing:
  clientId:
  clientSecret:
  keyId:
  appSecret:
  grantType: client_credentials
  scope: read
  tenantId:
  url:
    auth_url:
    api_url:
    token: /oauth/token
    get_device_unit_with_device_model: /api/deviceUnit/v1/getDeviceUnitWithDeviceModel
    device_type_list: /api/deviceType/v1/getDeviceTypeList
    get_device_unit_by_project: /api/deviceUnit/v1/getDeviceUnitByProject

wxminiapp:
  appid: wx2d50eb6a0ea95ae2
  appsecret: 7c91adccb60c70bb133138e51da8347c

jiguangpush:
  appkey: 5330ba3604458ef0f87fd072
  master_secret: 054d12b9479647cfa2ba5dce

photo:
  cache:
    directory: /temp/

# 文件临时目录配置 # 临时文件存储目录，可通过环境变量 linkapp_temp_dir 覆盖
file:
  temp:
    directory: /elk/data/shared/nginx/www/kz/kz/

