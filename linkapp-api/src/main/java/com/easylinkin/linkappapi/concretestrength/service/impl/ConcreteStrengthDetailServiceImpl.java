package com.easylinkin.linkappapi.concretestrength.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.common.utils.excel.ExcelConstant;
import com.easylinkin.linkappapi.common.utils.excel.ExcelTools;
import com.easylinkin.linkappapi.common.utils.io.OutputStreamUtil;
import com.easylinkin.linkappapi.concretestrength.mapper.ConcreteStrengthDetailMapper;
import com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthDetail;
import com.easylinkin.linkappapi.concretestrength.entity.vo.ConcreteStrengthDetailVo;
import com.easylinkin.linkappapi.concretestrength.service.ConcreteStrengthDetailService;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * ConcreteStrengthDetail表服务实现类
 *
 * <AUTHOR>
 * @date 2022/10/23
 */
@Slf4j
@Service("appConcreteStrengthDetailService")
public class ConcreteStrengthDetailServiceImpl extends ServiceImpl
        <ConcreteStrengthDetailMapper, ConcreteStrengthDetail> implements ConcreteStrengthDetailService {
    @Resource
    private CommonService commonService;
    @Resource
    private LinkappUserContextProducer linkappUserContextProducer;

    @Override
    public boolean saveOne(ConcreteStrengthDetail appConcreteStrengthDetail) {
        commonService.setCreateAndModifyInfo(appConcreteStrengthDetail);
       // appConcreteStrengthDetail.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        validParamRequired(appConcreteStrengthDetail);
        validRepeat(appConcreteStrengthDetail);
        validParamFormat(appConcreteStrengthDetail);
        return save(appConcreteStrengthDetail);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ConcreteStrengthDetail appConcreteStrengthDetail) {
        Assert.notNull(appConcreteStrengthDetail.getId(), "id不能为空");
        commonService.setModifyInfo(appConcreteStrengthDetail);
        //appConcreteStrengthDetail.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        validRepeat(appConcreteStrengthDetail);
        validParamFormat(appConcreteStrengthDetail);
        return updateById(appConcreteStrengthDetail);
    }

    @Override
    public IPage<ConcreteStrengthDetail> selectPage(Page page, ConcreteStrengthDetail appConcreteStrengthDetail) {
        //appConcreteStrengthDetail.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.selectPage(page, appConcreteStrengthDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        return removeByIds(idList);
    }

    @Override
    public void export(ConcreteStrengthDetail appConcreteStrengthDetail, HttpServletRequest request, HttpServletResponse
            response) {

        IPage<ConcreteStrengthDetail> page = selectPage(new Page(0, -1), appConcreteStrengthDetail);
        List<ConcreteStrengthDetail> records = page.getRecords();
        List
                <ConcreteStrengthDetailVo> appConcreteStrengthDetailVos = new ArrayList<>();
        for (ConcreteStrengthDetail expert : records) {
            appConcreteStrengthDetailVos.add(new ConcreteStrengthDetailVo(expert));
        }

        String keyValue = "名称:name,租户id:tenantId";
        String title = "ConcreteStrengthDetail导出数据";
        String fileName = title + ".xls";
        try {
            OutputStream outputStream = OutputStreamUtil
                    .getOutputStream(request, response, fileName);
            ExcelTools.exportExcel(outputStream, keyValue, appConcreteStrengthDetailVos, ExcelConstant.XLS, title);
            response.flushBuffer();
            outputStream.close();
        } catch (IOException e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！IOException异常" + e.getMessage());
        } catch (Exception e) {
            log.error("excel导出失败", e);
            throw new RuntimeException("excel导出失败！" + e.getMessage());
        }
    }

    @Override
    public ConcreteStrengthDetail getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ConcreteStrengthDetail appConcreteStrengthDetail) {
        /* QueryWrapper<ConcreteStrengthDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", appConcreteStrengthDetail.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<ConcreteStrengthDetail> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(appConcreteStrengthDetail.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!appConcreteStrengthDetail.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ConcreteStrengthDetail appConcreteStrengthDetail) {
        //Assert.notNull(appConcreteStrengthDetail, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(appConcreteStrengthDetail.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ConcreteStrengthDetail appConcreteStrengthDetail) {
        //Assert.isTrue(appConcreteStrengthDetail.getName() == null || appConcreteStrengthDetail.getName().length() <= 50,
        //        "名称超长");
    }
}

