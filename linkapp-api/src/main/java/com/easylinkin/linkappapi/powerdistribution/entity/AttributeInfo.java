package com.easylinkin.linkappapi.powerdistribution.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/16 18:17
 */
@Data
@Accessors(chain = true)
public class AttributeInfo {

    private String describe;

    private Double maxValue;
    private Date maxTime;

    private Double minValue;
    private Date minTime;

    private Double avgValue;

}
