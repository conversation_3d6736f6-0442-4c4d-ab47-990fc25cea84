package com.easylinkin.linkappapi.powerdistribution.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/11 11:42
 */
@Data
@Accessors(chain = true)
@TableName("distribution_company_info")
public class CompanyManageBean {

    @TableId(type= IdType.AUTO)
    private Integer id;

    @TableField(value = "company_name")
    private String companyName;

    @TableField(value = "contacts")
    private String contacts;

    @TableField(value = "phone")
    private String phone;

    @TableField(value = "remark")
    private String remark;

    /**
     * '创建人'
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * '修改人'
     */
    @TableField(value = "modifier")
    private String modifier;

    /**
     * '创建时间'
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * '修改时间'
     */
    @TableField(value = "modify_time")
    private Date modifyTime;

    /**
     * '租户ID'
     */
    @TableField(value = "tenant_id")
    private String tenantId;



    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    @TableField(exist = false)
    private String companyIds;

    @TableField(exist = false)
    private List<String> alarmContactIds;
}
