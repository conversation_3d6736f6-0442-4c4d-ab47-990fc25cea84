package com.easylinkin.linkappapi.powerdistribution.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * class info :
 *
 * <AUTHOR>
 * @date 2021/9/14 11:21
 */
@Data
@Accessors(chain = true)
public class LoadAnalyzeResult {

    /**
     * 负荷上下限
     */
    private Double[] upperAndLowerLimit;
    /**
     * 总视在功率流水
     */
    private List<ElectricityEs> esApparentPowerList;

    private Double maxApparentPower;

    /**
     * 日均值
     */
    private Double average;

    /**
     * 负荷分布统计结果
     */
    private Map<String, LoadDistributed> intervalMap;
}
