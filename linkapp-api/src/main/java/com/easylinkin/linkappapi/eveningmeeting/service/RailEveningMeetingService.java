package com.easylinkin.linkappapi.eveningmeeting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.eveningmeeting.dto.RailEveningMeetingDto;
import com.easylinkin.linkappapi.eveningmeeting.entity.RailEveningMeeting;
import com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingQueryVo;
import com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingVo;
import com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingCalendarVo;
import com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingCalendarQueryVo;
import com.easylinkin.linkappapi.eveningmeeting.vo.RailEveningMeetingGridSwitchVo;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.io.OutputStream;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 晚交班会主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface RailEveningMeetingService extends IService<RailEveningMeeting> {

  IPage<RailEveningMeetingVo> queryListByPage(RequestModel<RailEveningMeetingQueryVo> requestModel);

  /**
   * 增加
   * @param railEveningMeetingDto
   */
  void insert(RailEveningMeetingDto railEveningMeetingDto) throws JsonProcessingException;

  /**
   * 根据id编辑
   * @param railEveningMeetingDto
   */
  void updateOne(RailEveningMeetingDto railEveningMeetingDto);

  /**
   * @Description: 删除晚交班会（包含批量删除）
   * <AUTHOR>
   * @date 2025-07-02
   */
  void delBatch(List<Long> ids);

  /**
   * @Description: 根据id查询晚交班会详情
   * <AUTHOR>
   * @date 2025-07-02
   */
  RailEveningMeetingVo findById(Long id);

  /**
   * 协作人签字（可修改表单内容）
   * @param railEveningMeetingDto 晚交班会数据传输对象
   */
  void collaboratorSignWithUpdate(RailEveningMeetingDto railEveningMeetingDto);

  /**
   * 抄送人签字（仅签字）
   * @param meetingId 会议ID
   * @param signatureImageUrl 签名图片URL
   */
  void ccUserSign(Long meetingId, String signatureImageUrl);

  /**
   * @Description: 获取晚交班会日历数据
   * <AUTHOR>
   * @date 2025-07-02
   */
  List<RailEveningMeetingCalendarVo> getCalendarData(RailEveningMeetingCalendarQueryVo queryVo);

  /**
   * @Description: 获取网格点切换数据（显示所有网格点及其数据状态）
   * <AUTHOR>
   * @date 2025-07-02
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return 网格点列表及数据状态
   */
  List<RailEveningMeetingGridSwitchVo> getGridSwitchData(Date startTime, Date endTime);

  /**
   * 按月导出晚交班会台账（每个网格点一个Excel，打包zip）
   * @param yearMonth 月份（yyyy-MM）
   * @param out 输出流
   * @return 生成的文件名
   */
  String exportByMonth(String yearMonth,String tenantId, String key) throws Exception;

  /**
   * 根据文件名获取导出文件的完整路径
   * @param fileName 文件名
   * @return 完整文件路径
   */
  String getExportFilePath(String fileName);
}
