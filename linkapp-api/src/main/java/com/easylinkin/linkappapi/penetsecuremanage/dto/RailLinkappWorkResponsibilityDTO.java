package com.easylinkin.linkappapi.penetsecuremanage.dto;

import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkResponsibility;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19 下午 3:29
 */
@Data
public class RailLinkappWorkResponsibilityDTO extends RailLinkappWorkResponsibility {
    /**
     * 删除提交参数
     */
    List<RailLinkappWorkResponsibilityDTO> delList;
    /**
     * 新增提交参数
     */
    List<RailLinkappWorkResponsibilityDTO> addList;

    public static List<RailLinkappWorkResponsibilityDTO> creatExcel() {
        List<RailLinkappWorkResponsibilityDTO> list = new ArrayList<>();
        return list;
    }
}
