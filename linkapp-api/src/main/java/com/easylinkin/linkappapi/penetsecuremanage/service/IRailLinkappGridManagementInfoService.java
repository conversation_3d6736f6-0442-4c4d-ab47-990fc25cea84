package com.easylinkin.linkappapi.penetsecuremanage.service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappGridManagementInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.OrgaStructureChartVO;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappGridManagementInfoVO;

import java.util.List;
import java.util.Map;

public interface IRailLinkappGridManagementInfoService extends IService<RailLinkappGridManagementInfo>{

    IPage<RailLinkappGridManagementInfoVO> selectPage(Page page, RailLinkappGridManagementInfoDTO customQueryParams);

    List<RailLinkappGridManagementInfoVO> getList(RailLinkappGridManagementInfoDTO requestModel);

    List<RailLinkappGridManagementInfoVO> getListNoTenantId(RailLinkappGridManagementInfoDTO requestModel);

    List<String> saveList(RailLinkappGridManagementInfoDTO dto);

    void edit(RailLinkappGridManagementInfoDTO dto);

    OrgaStructureChartVO supervisoryOrgaStructureChart();

    void addMeetAndAnnouncement(List<RailLinkappGridManagementInfoDTO> addList);

    List<Map<String, Object>> getGridPersonInfo();
}
