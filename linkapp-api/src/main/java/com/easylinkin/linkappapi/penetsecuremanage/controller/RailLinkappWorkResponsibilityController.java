package com.easylinkin.linkappapi.penetsecuremanage.controller;

import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkInfo;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkInfoService;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.utils.ExcelHelper;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkResponsibilityVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 后台穿透式安全管理/职位职责接口类
 * <AUTHOR>
 * @date 2025/6/19 下午 4:35
 */
@RestController
@RequestMapping("/workResponsibility")
public class RailLinkappWorkResponsibilityController {
    @Autowired
    private IRailLinkappWorkResponsibilityService railLinkappWorkResponsibilityService;
    @Autowired
    private IRailLinkappWorkInfoService railLinkappWorkInfoService;
    /**职位职责列表查询接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/list")
    public RestMessage list(@RequestBody RailLinkappWorkResponsibilityDTO dto){
        List<RailLinkappWorkResponsibilityVO> result = railLinkappWorkResponsibilityService.getList(dto);
        return RestBuilders.successBuilder(result).build();
    }
    /**
     * @param dto
     * {
     *     workId://职位id
     *     delList://需要删除的记录
     *     addList://需要新增的记录
     * }
     * @return
     */
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody RailLinkappWorkResponsibilityDTO dto){
        if (StringUtils.isEmpty(dto.getWorkId())){
            return RestBuilders.failureMessage().setMessage("部门id不能为空");
        }
        if (dto.getAddList().size() == 0 && dto.getDelList().size() == 0){
            return RestBuilders.successMessage().setMessage("操作成功");
        }
        boolean result = railLinkappWorkResponsibilityService.saveToList(dto);
        if (result){
            return RestBuilders.successBuilder().build().setMessage("操作成功");
        }else{
            return RestBuilders.successBuilder().build().setMessage("操作失败");
        }
    }

    /**
     * @param request
     * @param response
     * @param workId //职位id
     * {
     *     "orgId":"dfasdf"//部门id
     * }
     */
    @GetMapping(value = "/expOne")
    public void expOne(HttpServletRequest request, HttpServletResponse response, String workId){
        if (StringUtils.isEmpty(workId)){
            return;
        }
        /**
         * 查询组织机构名称作为文件名
         */
        RailLinkappWorkInfo byId = railLinkappWorkInfoService.getById(workId);
        if (Objects.isNull(byId)){
            return;
        }
        RailLinkappWorkResponsibilityDTO dto = new RailLinkappWorkResponsibilityDTO();
        dto.setWorkId(workId);
        List<RailLinkappWorkResponsibilityVO>  result = railLinkappWorkResponsibilityService.getList(dto);
        List<Map<String,Object>> res_arrs = new ArrayList<>();
        for (RailLinkappWorkResponsibilityVO orgResponsibilityVO:result){
            Map<String, Object> map = new HashMap<>();
            map.put("content",orgResponsibilityVO.getContent());
            res_arrs.add(map);
        }
        String[] header_cn = { "职责内容"};
        String[] header = { "content"};
        ExcelHelper.exportData(header, header_cn, res_arrs, byId.getName()+".xlsx", request, response, 3);
    }

    /**
     *
     * @param dto
     * {
     *     "id":""//记录id
     *     "content":""//编辑内容
     *
     * }
     * @return
     */
    @PostMapping(value = "/edit")
    public RestMessage edit(@RequestBody RailLinkappWorkResponsibilityDTO dto){
        if (StringUtils.isEmpty(dto.getId())){
            return RestBuilders.failureMessage().setMessage("记录id不能为空");
        }
        RailLinkappWorkResponsibility byId = railLinkappWorkResponsibilityService.getById(dto.getId());
        if (Objects.isNull(byId)){
            return RestBuilders.failureMessage().setMessage("编辑的记录不存在");
        }
        railLinkappWorkResponsibilityService.edit(dto);
        return RestBuilders.successBuilder().build().setMessage("操作成功");
    }

    /**
     * 组织穿透导出当前租户下所有的工作职责
     * @param request
     * @param response
     */
    @GetMapping(value = "/expAll")
    public void expAll(HttpServletRequest request, HttpServletResponse response){
        railLinkappWorkResponsibilityService.expAll(request,response);
    }
}
