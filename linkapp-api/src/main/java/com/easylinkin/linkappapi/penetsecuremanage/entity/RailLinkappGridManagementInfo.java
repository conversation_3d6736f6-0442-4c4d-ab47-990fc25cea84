package com.easylinkin.linkappapi.penetsecuremanage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.common.translate.impl.DictTranslateor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/** 后台穿透式安全管理/网格信息
 * <AUTHOR>
 * @date 2025/6/20 下午 2:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_grid_management_info")
@CodeI18n
public class RailLinkappGridManagementInfo  extends Model<RailLinkappGridManagementInfo> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 工程类别 字典enginee_type
     */
    @TableField(value = "enginee_type")
    @Code2Text(translateor = DictTranslateor.class, value = "enginee_type")
    private String engineeType;
    /**
     * 网格编号
     */
    @TableField(value = "number")
    private Integer number;
    /**
     * 网格类别
     */
    @TableField(value = "grid_type")
    private String gridType;
    /**
     * 网格名称
     */
    @TableField(value = "grid_name")
    private String gridName;
    /**
     * 网格里程范围
     */
    @TableField(value = "mileage_range")
    private String mileageRange;
    /**
     * 选中安监专务人员id
     */
    @TableField(value = "safety_sup_id")
    private String safetySupId;
    /**
     * 选中安监专务人员姓名
     */
    @TableField(value = "safety_sup_name")
    private String safetySupName;
    /**
     * 选中安网格安全员id
     */
    @TableField(value = "grid_sec_id")
    private String gridSecId;
    /**
     * 选中安网格安全员姓名
     */
    @TableField(value = "grid_sec_name")
    private String gridSecName;
    /**
     * 选中的施工员/领工员人员id
     */
    @TableField(value = "grid_foreman_id")
    private String gridForemanId;
    /**
     * 选中的施工员/领工员人员姓名
     */
    @TableField(value = "grid_foreman_name")
    private String gridForemanName;
    /**
     * 选中的现场负责人/安全员id
     */
    @TableField(value = "grid_siteman_id")
    private String gridSitemanId;
    /**
     * 选中的现场负责人/安全员姓名
     */
    @TableField(value = "grid_siteman_name")
    private String gridSitemanName;
    /**
     * 高峰工作人员数量
     */
    @TableField(value = "staff_number")
    private Integer staffNumber;
    /**
     * 施工队伍
     */
    @TableField(value = "construction_team")
    private String constructionTeam;
    /**
     * 网格安全员是否专职 是 否
     */
    @TableField(value = "is_sole_duty")
    private String isSoleDuty;
    /**
     * 网格安全员持证情况
     */
    @TableField(value = "hold_cert")
    private String holdCert;
    /**
     * 网格安全员是否正式职工 是 否
     */
    @TableField(value = "is_formal_employee")
    private String isFormalEmployee;
    /**
     * 备注
     */
    @TableField(value = "notes")
    private String notes;
    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;
    /**
     * 创建人id
     */
    @TableField(value = "creator")
    private String creator;
    /**
     * 更新人id
     */
    @TableField(value = "modifier")
    private String modifier;
    /**
     * 提交时间创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     *更新时间
     */
    @TableField(value = "modify_time")
    private Date modifyTime;

    /**
     *紧急联系人id
     */
    @TableField(value = "grid_meet_id" ,updateStrategy = FieldStrategy.IGNORED)
    private String gridMeetId;
    /**
     * 紧急联系人姓名
     */
    @TableField(value = "grid_meet_name")
    private String gridMeetName;

    /**
     * 网格公示牌
     */
    @TableField(value = "announcement_file_url", updateStrategy = FieldStrategy.IGNORED)
    private String announcementFileUrl;
}
