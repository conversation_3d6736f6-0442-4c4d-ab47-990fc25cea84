package com.easylinkin.linkappapi.penetsecuremanage.controller;

import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgInfo;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappOrgInfoService;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappOrgResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.utils.ExcelHelper;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgResponsibilityVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 后台穿透式安全管理/部门职责接口类
 * <AUTHOR>
 * @date 2025/6/18 下午 5:11
 */
@RestController
@RequestMapping("/orgResponsibility")
public class RailLinkappOrgResponsibilityController {

    @Autowired
    private IRailLinkappOrgResponsibilityService railLinkappOrgResponsibilityService;

    @Autowired
    private IRailLinkappOrgInfoService railLinkappOrgInfoService;

    /**部门职责列表查询接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/list")
    public RestMessage list(@RequestBody RailLinkappOrgResponsibilityDTO dto){
        List<RailLinkappOrgResponsibilityVO>  result = railLinkappOrgResponsibilityService.getList(dto);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * @param dto
     * {
     *     orgId://部门id
     *     delList://需要删除的记录
     *     addList://需要新增的记录
     * }
     * @return
     */
    @PostMapping(value = "/save")
    public RestMessage save(@RequestBody RailLinkappOrgResponsibilityDTO dto){
        if (StringUtils.isEmpty(dto.getOrgId())){
            return RestBuilders.failureMessage().setMessage("部门id不能为空");
        }
       if (dto.getAddList().size() == 0 && dto.getDelList().size() == 0){
           return RestBuilders.successMessage().setMessage("操作成功");
       }
       boolean result  =  railLinkappOrgResponsibilityService.saveToList(dto);
       if (result){
           return RestBuilders.successBuilder().build().setMessage("操作成功");
       }else{
           return RestBuilders.successBuilder().build().setMessage("操作失败");
       }
    }

    /**
     * @param request
     * @param response
     * @param orgId
     * {
     *     "orgId":"dfasdf"//部门id
     * }
     */
    @GetMapping(value = "/expOne")
    public void expOne(HttpServletRequest request, HttpServletResponse response,String orgId){
        if (StringUtils.isEmpty(orgId)){
            return;
        }
        /**
         * 查询组织机构名称作为文件名
         */
        RailLinkappOrgInfo byId = railLinkappOrgInfoService.getById(orgId);
        if (Objects.isNull(byId)){
            return;
        }
        RailLinkappOrgResponsibilityDTO dto = new RailLinkappOrgResponsibilityDTO();
        dto.setOrgId(orgId);
        List<RailLinkappOrgResponsibilityVO>  result = railLinkappOrgResponsibilityService.getList(dto);
        List<Map<String,Object>> res_arrs = new ArrayList<>();
        for (RailLinkappOrgResponsibilityVO orgResponsibilityVO:result){
            Map<String, Object> map = new HashMap<>();
            map.put("content",orgResponsibilityVO.getContent());
            res_arrs.add(map);
        }
        String[] header_cn = { "职责内容"};
        String[] header = { "content"};
        ExcelHelper.exportData(header, header_cn, res_arrs, byId.getName()+".xlsx", request, response, 3);
    }

    /**
     *
     * @param dto
     * {
     *     "id":""//记录id
     *     "content":""//编辑内容
     *
     * }
     * @return
     */
    @PostMapping(value = "/edit")
    public RestMessage edit(@RequestBody RailLinkappOrgResponsibilityDTO dto){
        if (StringUtils.isEmpty(dto.getId())){
            return RestBuilders.failureMessage().setMessage("记录id不能为空");
        }
        RailLinkappOrgResponsibility byId = railLinkappOrgResponsibilityService.getById(dto.getId());
        if (Objects.isNull(byId)){
            return RestBuilders.failureMessage().setMessage("编辑的记录不存在");
        }
        railLinkappOrgResponsibilityService.edit(dto);
        return RestBuilders.successBuilder().build().setMessage("操作成功");
    }

    /**
     * 组织穿透导出当前租户下所有的部门职责
     * @param request
     * @param response
     */
    @GetMapping(value = "/expAll")
    public void expAll(HttpServletRequest request, HttpServletResponse response){
        railLinkappOrgResponsibilityService.expAll(request,response);
    }

}
