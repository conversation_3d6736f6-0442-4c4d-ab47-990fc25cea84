package com.easylinkin.linkappapi.penetsecuremanage.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.lobar.dto.excel.ExcelResultDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgResponsibilityVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IRailLinkappOrgResponsibilityService extends IService<RailLinkappOrgResponsibility> {
    /**
     * 列表查询
     * @param dto
     * @return
     */
    List<RailLinkappOrgResponsibilityVO> getList(RailLinkappOrgResponsibilityDTO dto);

    void initTemplate();

    boolean saveToList(RailLinkappOrgResponsibilityDTO dto);

    void edit(RailLinkappOrgResponsibilityDTO dto);

    void expAll(HttpServletRequest request, HttpServletResponse response);

    void importOrgResponsibilityExcel(String id, MultipartFile file, String tenantId);
}
