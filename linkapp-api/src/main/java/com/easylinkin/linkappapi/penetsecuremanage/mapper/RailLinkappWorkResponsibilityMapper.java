package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkResponsibilityVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappWorkResponsibilityMapper extends BaseMapper<RailLinkappWorkResponsibility>{

    List<RailLinkappWorkResponsibilityVO> getList(@Param("entity")RailLinkappWorkResponsibilityDTO dto);

    @Select("SELECT max(a.sort_index) from rail_linkapp_work_responsibility a where a.work_id=#{workId} and a.tenant_id=#{tenantId} ")
    Integer countIndexMaxByWorkId(@Param("workId")String workId,@Param("tenantId")String tenantId);

    @Delete("DELETE FROM rail_linkapp_work_responsibility WHERE work_id = #{workId} ")
    void deleteByWorkId(@Param("workId") String workId);
}
