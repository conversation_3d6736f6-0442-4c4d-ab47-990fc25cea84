package com.easylinkin.linkappapi.penetsecuremanage.dto;

import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgResponsibility;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18 下午 5:01
 */
@Data
public class RailLinkappOrgResponsibilityDTO extends RailLinkappOrgResponsibility {
    /**
     * 删除提交参数
     */
    List<RailLinkappOrgResponsibilityDTO> delList;
    /**
     * 新增提交参数
     */
    List<RailLinkappOrgResponsibilityDTO> addList;

    /**
     *
     * @return
     */

    public static List<RailLinkappOrgResponsibilityDTO> creatExcel() {
        List<RailLinkappOrgResponsibilityDTO> list = new ArrayList<>();
        return list;
    }
}
