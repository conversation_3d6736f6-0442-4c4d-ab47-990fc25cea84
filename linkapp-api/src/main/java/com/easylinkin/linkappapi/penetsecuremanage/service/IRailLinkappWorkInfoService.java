package com.easylinkin.linkappapi.penetsecuremanage.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IRailLinkappWorkInfoService extends IService<RailLinkappWorkInfo>{

    List<RailLinkappWorkInfoVO> getList(RailLinkappWorkInfoDTO dto);

    void edit(RailLinkappWorkInfoDTO dto);

    void sortIndexEdit(String id, Integer sortType);

    void del(String id);

    Integer countByOrgName(String tenantId, String workName);

    void add(String workName, MultipartFile file);
}
