package com.easylinkin.linkappapi.penetsecuremanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *  后台穿透式安全管理/职位表
 * <AUTHOR>
 * @date 2025/6/19 下午 3:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_work_info")
public class RailLinkappWorkInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /**
     * 职位名称
     */
    @TableField("name")
    private String name;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间/更新时间
     */
    @TableField("modify_time")
    private Date modifyTime;
    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;
    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 排序字段
     */
    @TableField("sort_index")
    private Integer sortIndex;


}
