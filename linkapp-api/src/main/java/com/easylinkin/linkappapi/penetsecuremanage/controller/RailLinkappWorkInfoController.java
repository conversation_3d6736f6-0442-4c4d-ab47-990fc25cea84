package com.easylinkin.linkappapi.penetsecuremanage.controller;

import com.easylinkin.linkappapi.common.utils.excel.ExcelConstant;
import com.easylinkin.linkappapi.common.utils.excel.ExcelTools;
import com.easylinkin.linkappapi.common.utils.io.OutputStreamUtil;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkInfo;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkInfoService;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkInfoVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Objects;

/**
 * 后台穿透式安全管理/职位信息类
 * <AUTHOR>
 * @date 2025/6/19 下午 4:36
 */
@RestController
@RequestMapping("/workInfo")
public class RailLinkappWorkInfoController {
    @Autowired
    private IRailLinkappWorkInfoService railLinkappWorkInfoService;
    @Autowired
    private IRailLinkappWorkResponsibilityService railLinkappWorkResponsibilityService;
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;
     /**职位信息列表查询接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/list")
    public RestMessage list(@RequestBody RailLinkappWorkInfoDTO dto){
        List<RailLinkappWorkInfoVO> result = railLinkappWorkInfoService.getList(dto);
        /**
         * 如果没有职责信息,进行责进行初始化
         */
        if(result.size() == 0){
            railLinkappWorkResponsibilityService.initTemplate();
            result = railLinkappWorkInfoService.getList(dto);
        }
        return RestBuilders.successBuilder(result).build();
    }


    /**
     * 职位名称编辑接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/edit")
    public RestMessage edit(@RequestBody RailLinkappWorkInfoDTO dto){
        if (StringUtils.isEmpty(dto.getId())){
            return RestBuilders.failureMessage("记录id不能为空");
        }
        String workName = dto.getName();
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer number = railLinkappWorkInfoService.countByOrgName(tenantId,workName);
        if (number>0){
            return RestBuilders.failureMessage("部门名称重复");
        }
        RailLinkappWorkInfo byId = railLinkappWorkInfoService.getById(dto.getId());
        if (Objects.isNull(byId)){
            return RestBuilders.failureMessage("编辑的记录不存在");
        }
        railLinkappWorkInfoService.edit(dto);
        return RestBuilders.successBuilder("操作成功").build();
    }

    /**
     *  职位信息排序设置接口
     * @param id 记录id
     * @param sortType 排序类型 0:上移动 1:下移动
     * @return
     */
    @GetMapping(value = "/sortIndex.edit")
    public RestMessage sortIndexEdit(String id,Integer sortType){
        if (StringUtils.isEmpty(id)){
            return RestBuilders.failureMessage("记录id不能为空");
        }
        if (Objects.isNull(sortType)){
            return RestBuilders.failureMessage("排序操作类型不能为空");
        }
        railLinkappWorkInfoService.sortIndexEdit(id,sortType);
        return RestBuilders.successBuilder("操作成功").build();
    }

    /**
     * 职位删除接口
     * @param id 记录id
     * @return
     */
    @GetMapping(value = "/del")
    public RestMessage del(String id){
        if (StringUtils.isEmpty(id)){
            return RestBuilders.failureMessage("记录id不能为空");
        }
        railLinkappWorkInfoService.del(id);
        return RestBuilders.successBuilder("操作成功").build();
    }


    /**
     *  组织穿透职位新增加导入职责接口
     * @param workName 部门名称
     * @param file    模板，需要导入的内容
     * @return
     */
    @PostMapping(value = "/add")
    public RestMessage add(@RequestPart("workName")String workName,@RequestPart(required = false,value = "file") MultipartFile file){
        if (StringUtils.isEmpty(workName)){
            return RestBuilders.failureMessage("职位名称不能为空不能为空");
        }
        if (workName.length()>10){
            return RestBuilders.failureMessage("部职位名称不能超过10个字符");
        }
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer number = railLinkappWorkInfoService.countByOrgName(tenantId,workName);
        if (number>0){
            return RestBuilders.failureMessage("部职名称重复");
        }
        railLinkappWorkInfoService.add(workName,file);
        return RestBuilders.successBuilder("操作成功").build();
    }
    /**
     *  组织穿透职位导入职责模板下载接口
     * @param request
     * @param response
     */
    @GetMapping(value = "/workInfoTemplate.down")
    public void rosterTemplateDown(HttpServletRequest request, HttpServletResponse response){
        String title = "职位职责信息";
        String fileName =  "workInfoTemplate.xlsx";
        try {
            OutputStream outputStream = OutputStreamUtil.getOutputStream(request, response, fileName);
            String keyValue = "序号:number,职责内容:content";
            ExcelTools.exportExcel(outputStream, keyValue, RailLinkappWorkResponsibilityDTO.creatExcel(), ExcelConstant.XLSX,title);
            response.flushBuffer();
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException("excel导出失败！IOException异常");
        } catch (Exception e) {
            throw new RuntimeException("excel导出失败！");
        }
    }


}
