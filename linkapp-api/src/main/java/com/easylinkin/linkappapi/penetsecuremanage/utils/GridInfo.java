package com.easylinkin.linkappapi.penetsecuremanage.utils;

import com.baomidou.mybatisplus.annotation.TableField;
import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.impl.DictTranslateor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/20 下午 7:24
 */
@Data
public class GridInfo {
    /**
     * 工程类别 字典enginee_type
     */
    private String engineeType;
    /**
     * 网格编号
     */
    private Integer number;
    /**
     * 网格类别
     */
    private String gridType;
    /**
     * 网格名称
     */
    private String gridName;
    /**
     * 网格里程范围
     */
    private String mileageRange;
    /**
     * 选中安监专务人员id
     */
    private String safetySupId;
    /**
     * 选中安监专务人员姓名
     */
    private String safetySupName;
    /**
     * 选中安网格安全员id
     */
    private String gridSecId;
    /**
     * 选中安网格安全员姓名
     */
    private String gridSecName;
    /**
     * 选中的施工员/领工员人员id
     */
    private String gridForemanId;
    /**
     * 选中的施工员/领工员人员姓名
     */
    private String gridForemanName;
    /**
     * 选中的现场负责人/安全员id
     */
    private String gridSitemanId;
    /**
     * 选中的现场负责人/安全员姓名
     */
    private String gridSitemanName;
    /**
     * 高峰工作人员数量
     */
    private Integer staffNumber;
    /**
     * 施工队伍
     */
    private String constructionTeam;
    /**
     * 网格安全员是否专职 是 否
     */
    private String isSoleDuty;
    /**
     * 网格安全员持证情况
     */
    private String holdCert;
    /**
     * 网格安全员是否正式职工 是 否
     */
    private String isFormalEmployee;
    /**
     * 备注
     */
    private String gridForemanPhone;
    private String gridSitemanPhone;
    private String gridSecPhone;
    /**
     * 备注
     */
    @TableField(value = "notes")
    private String notes;
}
