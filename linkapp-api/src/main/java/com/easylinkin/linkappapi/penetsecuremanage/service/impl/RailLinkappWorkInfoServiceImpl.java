package com.easylinkin.linkappapi.penetsecuremanage.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkInfo;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappWorkInfoMapper;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappWorkResponsibilityMapper;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkInfoService;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkInfoVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/6/19 下午 4:32
 */
@Service
@Slf4j
public class RailLinkappWorkInfoServiceImpl extends ServiceImpl<RailLinkappWorkInfoMapper, RailLinkappWorkInfo> implements IRailLinkappWorkInfoService {
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;
    @Autowired
    private RailLinkappWorkResponsibilityMapper workResponsibilityMapper;
    @Autowired
    private IRailLinkappWorkResponsibilityService railLinkappWorkResponsibilityService;
    @Resource
    private CommonService commonService;
    @Override
    public List<RailLinkappWorkInfoVO> getList(RailLinkappWorkInfoDTO dto) {
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.getList(dto);
    }

    @Override
    public void edit(RailLinkappWorkInfoDTO dto) {
        RailLinkappWorkInfo railLinkappWorkInfo = baseMapper.selectById(dto.getId());
        railLinkappWorkInfo.setName(dto.getName());
        baseMapper.updateById(railLinkappWorkInfo);
    }

    @Override
    public void sortIndexEdit(String id, Integer sortType) {
        RailLinkappWorkInfo railLinkappWorkInfo = baseMapper.selectById(id);
        if (Objects.isNull(railLinkappWorkInfo)){
            return;
        }
        RailLinkappWorkInfoDTO dto = new RailLinkappWorkInfoDTO();
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappWorkInfoVO> list = baseMapper.getList(dto);
        int index = 0 ;
        for (int i=0;i<list.size();i++){
            RailLinkappWorkInfoVO rgInfoVO = list.get(i);
            if (rgInfoVO.getId().equals(railLinkappWorkInfo.getId())){
                index = i;
            }
        }
        RailLinkappWorkInfo change = null;
        if (sortType.equals(0)){
            //如果是上移
            if (index == 0){
                //如果是第一个
                return;
            }
            RailLinkappWorkInfoVO rgInfoVO = list.get(index - 1);
            change = baseMapper.selectById(rgInfoVO.getId());
        }else if(sortType.equals(1)){
            //如果是下移
            if (index == list.size()-1){
                //如果最后一个
                return;
            }
            RailLinkappWorkInfoVO rgInfoVO = list.get(index + 1);
            change = baseMapper.selectById(rgInfoVO.getId());
        }
        if (Objects.isNull(change)){
            return;
        }
        Integer left_index = change.getSortIndex();
        Integer now_index = railLinkappWorkInfo.getSortIndex();
        int trs = 0;
        trs = Integer.valueOf(left_index);
        railLinkappWorkInfo.setSortIndex(trs);
        trs = Integer.valueOf(now_index);
        change.setSortIndex(trs);
        baseMapper.updateById(railLinkappWorkInfo);
        baseMapper.updateById(change);
    }
    /**
     * @param id
     */
    @Override
    public void del(String id) {
        baseMapper.deleteById(id);
        workResponsibilityMapper.deleteByWorkId(id);
    }

    @Override
    public Integer countByOrgName(String tenantId, String workName) {
        return baseMapper.countByOrgName(tenantId,workName);
    }

    @Override
    public void add(String workName, MultipartFile file) {
        //保存返回id
        //获取最大index
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer integer = baseMapper.countIndexMaxByTenantId(tenantId);
        if (Objects.isNull(integer)){
            integer = 0;
        }
        String id = UUID.randomUUID().toString().replaceAll("-", "");
        RailLinkappWorkInfo workinfo = new RailLinkappWorkInfo();
        workinfo.setName(workName);
        workinfo.setSortIndex((integer+1));
        workinfo.setTenantId(tenantId);
        commonService.setCreateAndModifyInfo(workinfo);
        workinfo.setId(id);
        baseMapper.insert(workinfo);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String key = "orgInfo:"+formatter.format(calendar.getTime());

        if (!Objects.isNull(file)){
            railLinkappWorkResponsibilityService.importWorkResponsibilityExcel(id,file,tenantId);
        }
    }

}
