package com.easylinkin.linkappapi.penetsecuremanage.controller;

import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.common.utils.DateUtil;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkflowResolveDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkflowResolve;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappWorkflowResolveService;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkflowResolveVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 后台穿透式安全管理/工作流程分解接口类
 * <AUTHOR>
 * @date 2025/6/21 上午 11:13
 */
@RestController
@RequestMapping("/workflowResolve")
public class RailLinkappWorkflowResolveController {
    @Autowired
    private IRailLinkappWorkflowResolveService workflowResolveService;
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;
    /**
     * 工作流程分解信息列表查询接口
     * @param dto
     * @return
     */
    @PostMapping(value = "/list")
    public RestMessage list(@RequestBody RailLinkappWorkflowResolveDTO dto){
       List<RailLinkappWorkflowResolveVO> result = workflowResolveService.getList(dto);
       return RestBuilders.successBuilder(result).build();
    }

    /**
     * 工作流程分解新增接口
     * @param dto
     * {
     *     "name":"",//工作流程名称
     *     "workflowFileUrl":""//多个逗号拼接
     * }
     * @return
     */
    @PostMapping(value = "/add")
    public RestMessage add(@RequestBody RailLinkappWorkflowResolveDTO dto){
        //名称前置校验
        if(StringUtils.isEmpty(dto.getName())){
//            return RestBuilders.failureMessage("工作流程名称不能为空");
            throw new BusinessException("工作流程名称不能为空");
        }
        if (StringUtils.isEmpty(dto.getWorkflowFileUrl())){
//            return RestBuilders.failureMessage("工作流程图不能为空");
            throw new BusinessException("工作流程图不能为空");
        }
        //名称前置校验
        if(dto.getName().length()>20){
//            return RestBuilders.failureMessage("工作流程名称不能超过20个字符");
            throw new BusinessException("工作流程名称不能超过20个字符");
        }
        String name = dto.getName();
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer number = workflowResolveService.countByOrgName(tenantId,name, dto.getId());
        if (number>0){
//            return RestBuilders.failureMessage("部门名称重复");
            throw new BusinessException("部门名称重复");
        }
        //新增
        workflowResolveService.add(dto);
        return RestBuilders.successBuilder("操作成功").build();
    }

    /**工作流程名称编辑接口
     * @param dto
     * {
     *     "id":""//
     *     "name":""//
     * }
     * @return
     */
    @PostMapping(value = "/edit")
    public RestMessage edit(@RequestBody RailLinkappWorkflowResolveDTO dto){
        //名称前置校验
        if(StringUtils.isEmpty(dto.getName())){
            return RestBuilders.failureMessage("工作流程名称不能为空");
        }
        if (StringUtils.isEmpty(dto.getId())){
            return RestBuilders.failureMessage("工作流程记录id不能为空");
        }
        //名称前置校验
        if(dto.getName().length()>20){
            return RestBuilders.failureMessage("工作流程名称不能超过20个字符");
        }
        String name = dto.getName();
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer number = workflowResolveService.countByOrgName(tenantId,name,dto.getId());
        if (number>0){
            return RestBuilders.failureMessage("工作流程名称重复");
        }
        RailLinkappWorkflowResolve byId = workflowResolveService.getById(dto.getId());
        if (Objects.isNull(byId)){
            return RestBuilders.failureMessage("编辑的记录不存在");
        }
        workflowResolveService.edit(dto);
        return RestBuilders.successBuilder("操作成功").build();
    }

    /**
     * 工作流程删除接口
     * @param id 删除记录id
     * @return
     */
    @GetMapping(value = "/del")
    public RestMessage  del(String id){
        if (StringUtils.isEmpty(id)){
            return RestBuilders.failureMessage("工作流程id不能为空");
        }
        workflowResolveService.removeById(id);
        return RestBuilders.successBuilder("操作成功").build();
    }

    /**
     * @param dto
     * {
     *     "id":""//需要下载的工作流程记录id
     * }
     * @return
     */
    @PostMapping(value = "/downloadOneByZip")
    public void downloadOneByZip(@RequestBody RailLinkappWorkflowResolveDTO dto, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(dto.getId())){
            throw new RuntimeException("工作流程记录不存在");
        }
        RailLinkappWorkflowResolve workflowResolve = workflowResolveService.getById(dto.getId());
        if (Objects.isNull(workflowResolve)){
            throw  new RuntimeException("工作流程记录不存在");
        }
        if (StringUtils.isEmpty(workflowResolve.getWorkflowFileUrl())){
           throw  new RuntimeException("下载文件不存在");
        }
        List<String> fileUrls = new ArrayList<>();
        RailLinkappWorkflowResolveDTO  workflowdto = new RailLinkappWorkflowResolveDTO();
        workflowdto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappWorkflowResolveVO> list = workflowResolveService.getList(workflowdto);
        for (RailLinkappWorkflowResolveVO vos:list){
            if (!StringUtils.isEmpty(vos.getWorkflowFileUrl())){
                String[] split = vos.getWorkflowFileUrl().split(",");
                for (String fl:split){
                    if (StringUtils.isEmpty(fl)){
                        continue;
                    }else{
                        fileUrls.add(fl);
                    }
                }
            }
        }
        if (fileUrls.size()>0){
           workflowResolveService.downloadOneByZip(fileUrls,response, workflowResolve.getName()+"_"+ DateUtil.getYYYYMMDDDate(new Date())+".zip");
        }
    }

}
