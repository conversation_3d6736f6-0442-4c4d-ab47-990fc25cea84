package com.easylinkin.linkappapi.penetsecuremanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *后台穿透式安全管理/职位职能信息
 * <AUTHOR>
 * @date 2025/6/19 下午 3:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_work_responsibility")
public class RailLinkappWorkResponsibility implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 记录id
     */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /**
     * 职位id
     */
    @TableField("work_id")
    private String workId;
    /**
     * 职责内容
     */
    @TableField("content")
    private String content;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 排序字段
     */
    @TableField("sort_index")
    private Integer sortIndex;
}
