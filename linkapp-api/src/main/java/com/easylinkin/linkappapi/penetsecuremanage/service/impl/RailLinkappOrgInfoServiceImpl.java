package com.easylinkin.linkappapi.penetsecuremanage.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.exceptions.BusinessException;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.common.utils.async.AsyncUtil;
import com.easylinkin.linkappapi.lobar.dto.excel.ExcelResultDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgInfo;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappOrgInfoMapper;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappOrgResponsibilityMapper;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappOrgInfoService;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappOrgResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgInfoVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/18 下午 5:06
 */
@Service
@Slf4j
public class RailLinkappOrgInfoServiceImpl extends ServiceImpl<RailLinkappOrgInfoMapper, RailLinkappOrgInfo> implements IRailLinkappOrgInfoService {
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;
    @Autowired
    private RailLinkappOrgResponsibilityMapper orgResponsibilityMapper;
    @Autowired
    private IRailLinkappOrgResponsibilityService railLinkappOrgResponsibilityService;
    @Resource
    private CommonService commonService;
    @Override
    public List<RailLinkappOrgInfoVO> getList(RailLinkappOrgInfoDTO dto) {
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.getList(dto);
    }

    @Override
    public void edit(RailLinkappOrgInfoDTO dto) {
        RailLinkappOrgInfo railLinkappOrgInfo = baseMapper.selectById(dto.getId());
        railLinkappOrgInfo.setName(dto.getName());
        baseMapper.updateById(railLinkappOrgInfo);
    }

    /**
     *
      * @param id
     * @param sortType
     */
    @Override
    public void sortIndexEdit(String id, Integer sortType) {
        RailLinkappOrgInfo railLinkappOrgInfo = baseMapper.selectById(id);
        if (Objects.isNull(railLinkappOrgInfo)){
            return;
        }
        RailLinkappOrgInfoDTO dto = new RailLinkappOrgInfoDTO();
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<RailLinkappOrgInfoVO> list = baseMapper.getList(dto);
       // Map<String,RailLinkappOrgResponsibilityVO> transForMap = new HashMap<>();
        int index = 0 ;
        for (int i=0;i<list.size();i++){
            RailLinkappOrgInfoVO rgInfoVO = list.get(i);
            if (rgInfoVO.getId().equals(railLinkappOrgInfo.getId())){
                index = i;
            }
        }
        RailLinkappOrgInfo change = null;
        if (sortType.equals(0)){
            //如果是上移
            if (index == 0){
                //如果是第一个
              return;
            }
            RailLinkappOrgInfoVO rgInfoVO = list.get(index - 1);
            change = baseMapper.selectById(rgInfoVO.getId());
        }else if(sortType.equals(1)){
            //如果是下移
            if (index == list.size()-1){
                //如果最后一个
                return;
            }
            RailLinkappOrgInfoVO rgInfoVO = list.get(index + 1);
            change = baseMapper.selectById(rgInfoVO.getId());
        }
        if (Objects.isNull(change)){
            return;
        }
        Integer left_index = change.getSortIndex();
        Integer now_index = railLinkappOrgInfo.getSortIndex();
        int trs = 0;
        trs = Integer.valueOf(left_index);
        railLinkappOrgInfo.setSortIndex(trs);
        trs = Integer.valueOf(now_index);
        change.setSortIndex(trs);
        baseMapper.updateById(railLinkappOrgInfo);
        baseMapper.updateById(change);
    }

    /**
     * @param id
     */
    @Override
    public void del(String id) {
        baseMapper.deleteById(id);
        orgResponsibilityMapper.deleteByOrgId(id);
    }

    @Override
    public Integer countByOrgName(String tenantId, String orgName) {
        return baseMapper.countByOrgName(tenantId,orgName);
    }

    @Override
    public void add(String orgName, MultipartFile file) {
        //保存返回id
        //获取最大index
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Integer integer = baseMapper.countIndexMaxByTenantId(tenantId);
        if (Objects.isNull(integer)){
            integer = 0;
        }
        String id = UUID.randomUUID().toString().replaceAll("-", "");
        RailLinkappOrgInfo orginfo = new RailLinkappOrgInfo();
        orginfo.setName(orgName);
        orginfo.setSortIndex((integer+1));
        orginfo.setTenantId(tenantId);
        commonService.setCreateAndModifyInfo(orginfo);
        orginfo.setId(id);
        baseMapper.insert(orginfo);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String key = "orgInfo:"+formatter.format(calendar.getTime());

        if (!Objects.isNull(file)){
            railLinkappOrgResponsibilityService.importOrgResponsibilityExcel(id,file,tenantId);
        }
    }

}
