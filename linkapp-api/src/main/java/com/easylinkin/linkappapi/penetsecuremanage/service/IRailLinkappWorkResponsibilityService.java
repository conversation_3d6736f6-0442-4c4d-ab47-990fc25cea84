package com.easylinkin.linkappapi.penetsecuremanage.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkResponsibilityVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IRailLinkappWorkResponsibilityService extends IService<RailLinkappWorkResponsibility>{
    void initTemplate();

    List<RailLinkappWorkResponsibilityVO> getList(RailLinkappWorkResponsibilityDTO dto);

    boolean saveToList(RailLinkappWorkResponsibilityDTO dto);

    void edit(RailLinkappWorkResponsibilityDTO dto);

    void expAll(HttpServletRequest request, HttpServletResponse response);

    void importWorkResponsibilityExcel(String id, MultipartFile file, String tenantId);

    List<RailLinkappWorkResponsibility> getByName(String jobName);
}
