package com.easylinkin.linkappapi.penetsecuremanage.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.easylinkin.linkappapi.common.service.CommonService;
import com.easylinkin.linkappapi.lobar.dto.excel.ExcelResultDetailDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.dto.ResponsibilityTempContentEnum;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgInfo;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappOrgInfoMapper;
import com.easylinkin.linkappapi.penetsecuremanage.mapper.RailLinkappOrgResponsibilityMapper;
import com.easylinkin.linkappapi.penetsecuremanage.service.IRailLinkappOrgResponsibilityService;
import com.easylinkin.linkappapi.penetsecuremanage.utils.ExcelHelper;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgInfoVO;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgResponsibilityVO;
import com.easylinkin.linkappapi.security.context.LinkappUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/18 下午 5:06
 */
@Service
@Slf4j
public  class RailLinkappOrgResponsibilityServiceImpl extends ServiceImpl<RailLinkappOrgResponsibilityMapper, RailLinkappOrgResponsibility> implements IRailLinkappOrgResponsibilityService {
    @Autowired
    private LinkappUserContextProducer linkappUserContextProducer;
    @Autowired
    private RailLinkappOrgInfoMapper orgInfoMapper;
    @Resource
    private CommonService commonService;
    @Override
    public List<RailLinkappOrgResponsibilityVO> getList(RailLinkappOrgResponsibilityDTO dto) {
        dto.setTenantId(linkappUserContextProducer.getNotNullCurrent().getTenantId());
        return baseMapper.getList(dto);
    }

    /**
     * 初始化部门职能
     */
    @Override
    public void initTemplate() {
        List<ResponsibilityTempContentEnum> types = ResponsibilityTempContentEnum.getTypes("1");
        if (types.size() == 0){
            return;
        }
        Date now = new Date();
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Map<String, List<String>> maps = new HashMap<>();
        Map<String, Integer> maps_upIndex = new HashMap<>();
        Map<String, Integer> maps_index = new HashMap<>();
        for (ResponsibilityTempContentEnum type_e:types){
            String name = type_e.getName();
            List<String> strings = maps.get(name);
            if (Objects.isNull(strings)){
                strings = new ArrayList<>();
                strings.add(type_e.getContent());
                maps_upIndex.put(name, type_e.getUpIndex());
            }else{
                strings.add(type_e.getContent());
            }
            maps.put(name,strings);
            maps_index.put(type_e.getContent(), type_e.getIndex());
        }
        for (Map.Entry<String, List<String>> entry:maps.entrySet()) {
            String key = entry.getKey();
            RailLinkappOrgInfo orgInfo = new RailLinkappOrgInfo();
            String orgId = UUID.randomUUID().toString().replaceAll("-","");
            orgInfo.setId(orgId);
            orgInfo.setName(key);
            orgInfo.setSortIndex(maps_upIndex.get(key)==null?1:maps_upIndex.get(key));
            orgInfo.setTenantId(tenantId);
            commonService.setCreateAndModifyInfo(orgInfo);
            orgInfoMapper.insert(orgInfo);
            List<String> value = entry.getValue();
            for (String content:value){
                RailLinkappOrgResponsibility  orgResponsibility = new RailLinkappOrgResponsibility();
                String id = UUID.randomUUID().toString().replaceAll("-","");
                orgResponsibility.setId(id);
                orgResponsibility.setOrgId(orgId);
                orgResponsibility.setCreateTime(now);
                orgResponsibility.setTenantId(tenantId);
                orgResponsibility.setContent(content);
                orgResponsibility.setSortIndex(maps_index.get(content)==null?1:maps_index.get(content));
                baseMapper.insert(orgResponsibility);
            }
        }
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public boolean saveToList(RailLinkappOrgResponsibilityDTO dto) {
        try {
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        /**
         * 删除
         */
        List<RailLinkappOrgResponsibilityDTO> dels = dto.getDelList();
        for (RailLinkappOrgResponsibilityDTO delDTO:dels){
            if (!StringUtils.isEmpty(delDTO.getId())){
                 baseMapper.deleteById(delDTO.getId());
            }
        }
        /**
         * 新增
         */
        Date date = new Date();
        //获取 最大索引
        Integer iMax = baseMapper.countIndexMaxByOrgId(dto.getOrgId(), tenantId);
        if (Objects.isNull(iMax)){
            iMax = 0;
        }
        List<RailLinkappOrgResponsibilityDTO> addls = dto.getAddList();
        for (RailLinkappOrgResponsibility addDTO:addls){
            RailLinkappOrgResponsibility responsibility  = new RailLinkappOrgResponsibility();
            String id = UUID.randomUUID().toString().replaceAll("-","");
            responsibility.setId(id);
            responsibility.setOrgId(dto.getOrgId());
            responsibility.setCreateTime(date);
            responsibility.setTenantId(tenantId);
            responsibility.setContent(addDTO.getContent());
            responsibility.setSortIndex((iMax+1));
            commonService.setCreateAndModifyInfo(responsibility);
            baseMapper.insert(responsibility);
            iMax =  responsibility.getSortIndex();
        }
        }catch (Exception e){
            return false;
        }
        return true;
    }

    /**
     * 内容编辑
     * @param dto
     */
    @Override
    public void edit(RailLinkappOrgResponsibilityDTO dto) {
        RailLinkappOrgResponsibility railLinkappOrgResponsibility = baseMapper.selectById(dto.getId());
        railLinkappOrgResponsibility.setContent(dto.getContent());
        baseMapper.updateById(railLinkappOrgResponsibility);
    }

    @Override
    public void expAll(HttpServletRequest request, HttpServletResponse response) {
        String tenantId = linkappUserContextProducer.getNotNullCurrent().getTenantId();
        Map<String, List<Map<String,Object>>> transForMap = new HashMap<>();
        List<Map<String,Object>> bookNames  = new ArrayList<>();
        //查询所有的工作职责
        RailLinkappOrgInfoDTO orgInfoDTO = new RailLinkappOrgInfoDTO();
        orgInfoDTO.setTenantId(tenantId);
        List<RailLinkappOrgInfoVO> list = orgInfoMapper.getList(orgInfoDTO);
        if (list.size() == 0){
            return;
        }
        String valueName = "orgName";
        String keyId = "orgId";
        for (RailLinkappOrgInfoVO orgInfoVO:list){
            transForMap.put(orgInfoVO.getId(),new ArrayList<Map<String,Object>>());
            Map<String, Object> workName = new HashMap<>();
            workName.put(valueName,orgInfoVO.getName());
            workName.put(keyId,orgInfoVO.getId());
            bookNames.add(workName);
        }
        //查询所有的职责内容
        RailLinkappOrgResponsibilityDTO workResponsibilityDTO  = new RailLinkappOrgResponsibilityDTO();
        workResponsibilityDTO.setTenantId(tenantId);
        List<RailLinkappOrgResponsibilityVO> railLinkappOrgResponsibilityVOs = baseMapper.getList(workResponsibilityDTO);
        for (RailLinkappOrgResponsibilityVO rvo:railLinkappOrgResponsibilityVOs){
            List<Map<String, Object>> maps = transForMap.get(rvo.getOrgId());
            if (!Objects.isNull(maps)){
                Map<String, Object> lmap = new HashMap<>();
                lmap.put("content",rvo.getContent());
                maps.add(lmap);
                transForMap.put(rvo.getOrgId(),maps);
            }
        }
        String[] header_cn = { "职责内容"};
        String[] header = { "content"};
        String fileName = "部门职责.xlsx";
        ExcelHelper.Multipleworkbooks(header_cn,header,request,response,fileName,transForMap,bookNames,3,valueName,keyId);
    }

    @Override
    public void importOrgResponsibilityExcel(String orgId, MultipartFile file, String tenantId) {
      //  ExcelResultDTO res = new ExcelResultDTO();
        try {
            List<ExcelResultDetailDTO> excelResultDetailDTOS = new ArrayList<ExcelResultDetailDTO>();
            InputStream inputStream = file.getInputStream();
            Workbook workbook = WorkbookFactory.create(inputStream);
            log.debug("共有 " + workbook.getNumberOfSheets() + " Sheets : ");
            int iMax = 0;
            //循环工作簿
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                //Date createTime = new Date();
                int rows = sheet.getPhysicalNumberOfRows();
                for (int row = 2; row < rows; row++) {
                    Row row_n = sheet.getRow(row);
                    if (row_n != null) {
                        //index++;
                        String index  = getValue(row_n.getCell(0)); //排序//忽略
                        String content  = getValue(row_n.getCell(1)); //内容
                        if (!StringUtils.isEmpty(content)){
                            RailLinkappOrgResponsibility responsibility  = new RailLinkappOrgResponsibility();
                            String id = UUID.randomUUID().toString().replaceAll("-","");
                            responsibility.setId(id);
                            responsibility.setOrgId(orgId);
                            responsibility.setTenantId(tenantId);
                            responsibility.setContent(content);
                            responsibility.setSortIndex((iMax+1));
                            commonService.setCreateAndModifyInfo(responsibility);
                            baseMapper.insert(responsibility);
                            iMax =  responsibility.getSortIndex();
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private String getValue(Cell xSSFCell) {
        if (null == xSSFCell) {
            return "";
        }
        try {
            return String.valueOf(xSSFCell.getStringCellValue());
        } catch (Exception ex) {
        }
        return "";
    }
}
