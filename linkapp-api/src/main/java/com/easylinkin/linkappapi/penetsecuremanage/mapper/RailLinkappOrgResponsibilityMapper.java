package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgResponsibilityDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgResponsibility;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgResponsibilityVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappOrgResponsibilityMapper  extends BaseMapper<RailLinkappOrgResponsibility> {

    List<RailLinkappOrgResponsibilityVO> getList(@Param("entity") RailLinkappOrgResponsibilityDTO dto);

    @Select("SELECT max(a.sort_index) from rail_linkapp_org_responsibility a where a.org_id=#{orgId} and a.tenant_id=#{tenantId} ")
    Integer countIndexMaxByOrgId(@Param("orgId")String orgId,@Param("tenantId")String tenantId);

    @Delete("DELETE FROM rail_linkapp_org_responsibility WHERE org_id = #{orgId} ")
    void deleteByOrgId(@Param("orgId") String orgId);
}
