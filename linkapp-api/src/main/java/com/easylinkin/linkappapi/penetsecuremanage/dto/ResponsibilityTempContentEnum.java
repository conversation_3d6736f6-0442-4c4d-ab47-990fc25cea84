package com.easylinkin.linkappapi.penetsecuremanage.dto;

import java.util.ArrayList;
import java.util.List;

public enum ResponsibilityTempContentEnum {
    /**
     * 一、安全管理部
     */
    ORG_INFO_1("1","安全管理部","组织开展全员安全生产教育、典型事故案例警示教育,检查班前安全讲话落实情况",1,1),
    ORG_INFO_2("1","安全管理部","组织开展危险源辨识与评估,发现存在偏差时应及时纠偏完善。督促落实现场重大危险源的安全管理措施。",2,1),
    ORG_INFO_3("1","安全管理部","组织开展安全生产综合检查或专项检查,及时通报安全隐患,督促相关业务部门、施工员(领工员)等限期整改闭合。",3,1),
    ORG_INFO_4("1","安全管理部","巡查网格安全生产状况,制止和纠正违章指挥、强令冒险作业、违反操作规程的行为。",4,1),
    ORG_INFO_5("1","安全管理部","负责网格划分、网格安全员资格初审、动态调整上报及公示等。",5,1),
    /**
     * 二、生产管理部
     */
    ORG_INFO_6("1","生产管理部","监督施工计划执行情况,检查并制止盲目抢工现象,按照设计图纸、施工技术方案、作业指导书组织施工,纠正施工计划和资源配置不合理情况。相关检查结果与施工员(领工员)当月绩效挂钩。",1,2),
    ORG_INFO_7("1","生产管理部","落实现场重大危险源的安全管理措施,重大风险或超危大工程施工时应落实现场监督人员。",2,2),
    ORG_INFO_8("1","生产管理部","组织施工员(领工员)落实生产过程中各项安全管理制度和安全措施。",3,2),
    ORG_INFO_9("1","生产管理部","组织施工员(领工员)开展安全隐患自查自纠及整改落实。",4,2),
    ORG_INFO_10("1","生产管理部","参与施工过程中危险源辨识和评价,落实危险源控制措施。",5,2),
    ORG_INFO_11("1","生产管理部","组织施工员(领工员)开展班前讲话、班后碰头会。",6,2),
    ORG_INFO_12("1","生产管理部","参与应急演练,发生事故或险情时组织应急疏散、逃生。",7,2),
    ORG_INFO_13("1","生产管理部","组织施工员(领工员)、施工作业队伍开展关键施工作业安全条件确认。",8,2),
    /**
     * 三、工程技术管理部
     */
    ORG_INFO_14("1","程技术管理部","负责检查各类施工技术方案、作业指导书的现场落实情况,及时纠正落实偏差;发现在施工方案安全措施不足时,应及时纠善。",1,3),
    ORG_INFO_15("1","程技术管理部","组织开展安全风险分级管控,制定风险管控措施。",2,3),
    ORG_INFO_16("1","程技术管理部","负责监督管理网格内技术安全管理工作,监督检查安全技术交底落实情况,检工程质量安全技术措施的实施情况。相关检查结果与施工员(领工员)当月绩效挂钩。",3,3),
    /**
     * 四、质量管理部
     */
    ORG_INFO_17("1","质量管理部","检查现场不按设计图纸施工、把不合格品当合格品验收、把不合格材料用于工程等违规违法行为,发现质量安全隐患时应予以制止并督促施工员(领工员)组织整改闭合检查结果与施工员(领工员)当月绩效挂钩。",1,4),
    /**
     * 五、机械设备部
     */
    ORG_INFO_18("1","机械设备部","按规定配备特种设备安全管理人员,检查机械设备作业安全条件,对机械设备定期开展检查、维修、保养和检测,及时消除机械设备安全隐患。",1,5),
    ORG_INFO_19("1","机械设备部","对机械作业人员开展安全教育培训,并组织特种设备作业人员进行演练,检查机械作业人员履职情况。协调解决机械设备作业条件安全隐患。",2,5),
    ORG_INFO_20("1","机械设备部","组织开展机械设备进场验收、报验,检查机械安全防护设施及安全警示标识张贴情况等。",3,5),
    /**
     * 六、物资管理部
     */
    ORG_INFO_21("1","物资管理部","负责进场、存储和分发等环节的物资质量安全符合标准。",1,6),
    ORG_INFO_22("1","物资管理部","负责物资周转过程安全。",2,6),
    ORG_INFO_23("1","物资管理部","负责应急物资采购等。",3,6),
    /**
     * 七、计划合同管理部
     */
    ORG_INFO_24("1","计划合同管理部","负责分包企业资格审查。",1,7),
    ORG_INFO_25("1","计划合同管理部","检查并落实分包企业现场主要人员履约情况。",2,7),
    ORG_INFO_26("1","计划合同管理部","负责安全生产费归口管理等。",3,7),
    /**
     * 职位职责
     */
    /**
     *  一、网格安全员
     */
    WORK_INFO_1("2","网格安全员","督促生产过程中各项安全管理制度和方案交底中安全措施的落实,现场施工满足设计图纸、施工方案、作业指导书要求的,及时上报问题,并跟踪整改落实。",1,1),
    WORK_INFO_2("2","网格安全员","参与施工过程中危险源辨识和评价,督促落实危险源控制措施。",2,1),
    WORK_INFO_3("2","网格安全员","开展班前安全检查,篮督开展班前讲话,参每日班后总结会。",3,1),
    WORK_INFO_4("2","网格安全员","督促检查施工员(领工员)及班组长开展关键施工作业安全条件确认。",4,1),
    WORK_INFO_5("2","网格安全员","监督检查作业人员“三违”行为,并及时制止。",5,1),
    WORK_INFO_6("2","网格安全员","监督检查机械器具报验、本体安全状况、安全装置配备及安全用电等情况。",6,1),
    WORK_INFO_7("2","网格安全员","监督检查作业中安全防护设施、警示标志、照明、有毒有害气体监测、周边建(构)筑物状态等环境条件。",7,1),
    WORK_INFO_8("2","网格安全员","对施工现场存在的安全隐患有权责令立即整改,督促并复查隐患整改情况,做好检查记录。",8,1),
    WORK_INFO_9("2","网格安全员","及时向安监办报告现场安全生产监督情况,向各业务部门反映需要协调解决的问题。",9,1),
    WORK_INFO_10("2","网格安全员","参与危险性较大分部分项工程安全专项方案的现场监督与阶段性验收。",10,1),
    WORK_INFO_11("2","网格安全员","如遇极端恶劣天气或重大事故隐患,及时停止现场施工,督促人员撤离至安全区域。",11,1),
    WORK_INFO_12("2","网格安全员","参与应急演练,发生事故或险情时组织应急疏散、逃生。",12,1),
    WORK_INFO_13("2","网格安全员","其他涉及安全管理的相关工作。",13,1),
    /**
     * 二、安监办主任
     */
    WORK_INFO_14("2","安监办主任","负责组建安监办，配置安监办常务副主任、副主任、安监专务。",1,2),
    WORK_INFO_15("2","安监办主任","组织制定并实施项目安全穿透式监督管理制度及考核办法。",2,2),
    WORK_INFO_16("2","安监办主任","审批安全穿透式监督管理实施方案、安全监督计划。",3,2),
    WORK_INFO_17("2","安监办主任","审批项目安全监督报告,并每季度末上报施工企业(工程局集团公司)。",4,2),
    WORK_INFO_18("2","安监办主任","组织开展安全穿透式管理考核工作。",5,2),
    /**
     * 三、安监办常务副主任
     */
    WORK_INFO_19("2","安监办常务副主任","督促落实项目安全穿透式监督管理各项制度",1,3),
    WORK_INFO_20("2","安监办常务副主任","审核安全穿透式监督管理实施方案、安全监督计划,组织安监专务开安全监督工作",2,3),
    WORK_INFO_21("2","安监办常务副主任","每月组织召开项目安全穿透式监督专题会议,通报监督情况",3,3),
    WORK_INFO_22("2","安监办常务副主任","审核项目安全监督报告",4,3),
    /**
     * 四、安监办副主任
     */
    WORK_INFO_23("2","安监办副主任","执行项目安全穿透式监督管理各项制度",1,4),
    WORK_INFO_24("2","安监办副主任","负责项目安全监督的日常管理工作",2,4),
    WORK_INFO_25("2","安监办副主任","组织制订安全穿透式监督管理实施方案、安全监督计划并严格执行",3,4),
    WORK_INFO_26("2","安监办副主任","每季度末编制安全监督报告",4,4),
    WORK_INFO_27("2","安监办副主任","具体开展项目安全穿透式管理考核工作",5,4),
    WORK_INFO_28("2","安监办副主任","及时收集掌握现场安监专务的监督情况,收到重大事故隐患报告后及时到现场进行核实处理。",6,4),
    /**
     * 五、安监专务
     */
    WORK_INFO_29("2","安监专务","监督网格安全员出勤在岗情况。",1,5),
    WORK_INFO_30("2","安监专务","监督相关管理人员跟班作业情况。",2,5),
    WORK_INFO_31("2","安监专务","监督班前讲话活动开展情况。",3,5),
    WORK_INFO_32("2","安监专务","监督作业人员实名制管理情况。",4,5),
    WORK_INFO_33("2","安监专务","监督网格内人员安全教育培训情况。",5,5),
    WORK_INFO_34("2","安监专务","监督关键施工作业安全条件确认及过程安全盯控情况。",6,5),
    WORK_INFO_35("2","安监专务","监督安全风险管控措施落实及隐患整改情况。",7,5),
    WORK_INFO_36("2","安监专务","监督施工员(领工员)班后总结开展情况。",8,5),
    WORK_INFO_37("2","安监专务","监督网格安全员班后对现场安全条件确认情况。",9,5),
    WORK_INFO_38("2","安监专务","及时填写安全监督记录,形成工作台账,并每月底上报安全监督情况。",10,5),
    WORK_INFO_39("2","安监专务","监督各业务部门安全检查频次。",11,5),
    WORK_INFO_40("2","安监专务","监督工程技术管理部门检查施工技术方案、作业指导书、安全技术交底、工程质量安全技术措施的落实情况。",12,5),
    WORK_INFO_41("2","安监专务","监督质量管理部门检查整治质量隐患情况。",13,5),
    WORK_INFO_42("2","安监专务","监督生产组织部门资源配置、落实施工方案与作业指导书以及盲目抢工情况。",14,5),
    WORK_INFO_43("2","安监专务","监督机械设备管理部门特种设备安全管理人员配置、机械设备进场验收机械备检查维修保养、机械作业人员安全教育培训等情况。",15,5),
    WORK_INFO_44("2","安监专务","监督物资管理部门物资收发存过程中的质量安全管控情况,应急物资储备情况。",16,5),
    WORK_INFO_45("2","安监专务","监督安全管理部门全员安全生产教育和培训、危险源辨识和评估、隐患排查治理、应急救援预案制定及演练、网格划分及网格安全员信息等情况。",17,5),
    WORK_INFO_46("2","安监专务","监督计划合同管理部门分包企业资格审查、分包企业现场主要人员履约履职检查、安全生产费用使用管理等情况。",18,5);

    /**
     * 1:部门类型
     * 2:工作类型
     */
    String type;
    /**
     * 职能单位名称
     */
    String name;
    /**
     * 职能内容
     */
    String content;
    /**
     * 内容排序
     */
    Integer index;
    /**
     * 上级排序
     */
    Integer upIndex;

    /**
     *
     * @param type
     * @param name
     * @param content
     */

    ResponsibilityTempContentEnum(String type, String name, String content,Integer index,Integer upIndex) {
        this.type = type;
        this.name = name;
        this.content = content;
        this.index = index;
        this.upIndex = upIndex;
    }

    public String getType() {
        return type;
    }

    public Integer getIndex() {
        return index;
    }

    public Integer getUpIndex() {
        return upIndex;
    }

    public void setUpIndex(Integer upIndex) {
        this.upIndex = upIndex;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public static List<ResponsibilityTempContentEnum> getTypes(String type_p){
        List<ResponsibilityTempContentEnum> result = new ArrayList<>();
        ResponsibilityTempContentEnum[] values = ResponsibilityTempContentEnum.values();
        for (ResponsibilityTempContentEnum e:values){
            if (e.getType().equals(type_p)){
                result.add(e);
            }
        }
        return result;
    }
}
