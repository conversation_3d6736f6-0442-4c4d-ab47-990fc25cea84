package com.easylinkin.linkappapi.penetsecuremanage.vo;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 下午 12:03
 */
@Data
public class OrgaStructureChartVO {

    private String projectId;

    private String projectName;

    private List<OrgaStructureStaffVO> levelOne;

    List<OrgaStructureStaffVO>  levelTwo;
    /**
     * 如果没有安监专务，网格信息需要放进这个数组
     */
    private List<GridExpVO>  levelFour;

    List<RailLinkappOrgInfoVO>  orgs;

}
