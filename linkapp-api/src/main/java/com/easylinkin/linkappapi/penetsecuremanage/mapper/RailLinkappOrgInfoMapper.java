package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgInfoVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappOrgInfoMapper extends BaseMapper<RailLinkappOrgInfo>{
    List<RailLinkappOrgInfoVO> getList(@Param("entity") RailLinkappOrgInfoDTO dto);

    @Select("SELECT COUNT(1) FROM rail_linkapp_org_info WHERE tenant_id = #{tenantId} and name =#{name}")
    Integer countByOrgName(@Param("tenantId")  String tenantId,@Param("name")  String name);

    @Select("SELECT max(a.sort_index) from rail_linkapp_org_info a where a.tenant_id=#{tenantId} ")
    Integer countIndexMaxByTenantId(@Param("tenantId")String tenantId);

    @Select("SELECT a.* from rail_linkapp_org_info a where a.tenant_id=#{tenantId}  order by a.sort_index asc ")
    List<RailLinkappOrgInfoVO> selectByTenantIdList(@Param("tenantId")String tenantId);

}
