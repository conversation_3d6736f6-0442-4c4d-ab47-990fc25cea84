package com.easylinkin.linkappapi.penetsecuremanage.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappOrgInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappOrgInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappOrgInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IRailLinkappOrgInfoService extends IService<RailLinkappOrgInfo> {

    List<RailLinkappOrgInfoVO> getList(RailLinkappOrgInfoDTO dto);

    void edit(RailLinkappOrgInfoDTO dto);

    void sortIndexEdit(String id, Integer sortType);

    void del(String id);

    Integer countByOrgName(String tenantId, String orgName);

    void add(String orgName, MultipartFile file);
}
