package com.easylinkin.linkappapi.penetsecuremanage.entity;

/**
 * <AUTHOR>
 * @date 2025/6/18 下午 4:50
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 后台穿透式安全管理/部门职责表
 * <AUTHOR>
 * @date 2025/6/18 下午 4:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_org_responsibility")
public class RailLinkappOrgResponsibility implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 记录id
     */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /**
     * 管理部门id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 职责内容
     */
    @TableField("content")
    private String content;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 排序字段
     */
    @TableField("sort_index")
    private Integer sortIndex;

}
