package com.easylinkin.linkappapi.penetsecuremanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**后台穿透式安全管理/工作流程分解
 * <AUTHOR>
 * @date 2025/6/21 上午 10:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_linkapp_workflow_resolve")
public class RailLinkappWorkflowResolve implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /**
     * 工作职责名称
     */
    @TableField("name")
    private String name;
    /**
     * 流程图地址（pdf/图片）
     */
    @TableField("workflow_file_url")
    private String workflowFileUrl;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 排序字段
     */
    @TableField("sort_index")
    private Integer sortIndex;

}
