package com.easylinkin.linkappapi.penetsecuremanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkflowResolveDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkflowResolve;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkflowResolveVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IRailLinkappWorkflowResolveService  extends IService<RailLinkappWorkflowResolve> {
    List<RailLinkappWorkflowResolveVO> getList(RailLinkappWorkflowResolveDTO dto);
    Integer countByOrgName(String tenantId, String name, String id);

    void add(RailLinkappWorkflowResolveDTO dto);

    void edit(RailLinkappWorkflowResolveDTO dto);

    void downloadOneByZip(List<String> fileUrls, HttpServletResponse response,String  zipfileName) throws IOException;
}
