package com.easylinkin.linkappapi.penetsecuremanage.dto;

import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 下午 2:23
 */
@Data
public class RailLinkappGridManagementInfoDTO extends RailLinkappGridManagementInfo {
    /**
     * 新增需要传入的数组
     */
   private List<RailLinkappGridManagementInfoDTO> addList;
}
