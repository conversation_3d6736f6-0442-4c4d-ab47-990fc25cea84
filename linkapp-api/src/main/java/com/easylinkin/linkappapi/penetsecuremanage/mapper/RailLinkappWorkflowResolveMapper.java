package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkflowResolveDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkflowResolve;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkflowResolveVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappWorkflowResolveMapper extends BaseMapper<RailLinkappWorkflowResolve>{
    List<RailLinkappWorkflowResolveVO> getList(@Param("entity") RailLinkappWorkflowResolveDTO dto);

    //添加一个 if 条件。验证 id 如果 id 传了。那么 id 不等于 id
    Integer countByOrgName(@Param("tenantId")  String tenantId,@Param("name")  String name,@Param("id")  String id);

    @Select("SELECT max(a.sort_index) from rail_linkapp_workflow_resolve a where a.tenant_id=#{tenantId} ")
    Integer countIndexMaxByTenantId(@Param("tenantId")String tenantId);
}
