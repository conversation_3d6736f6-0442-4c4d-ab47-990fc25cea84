package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappWorkInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappWorkInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkInfoVO;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappWorkResponsibilityVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RailLinkappWorkInfoMapper extends BaseMapper<RailLinkappWorkInfo>{
    List<RailLinkappWorkInfoVO> getList(@Param("entity") RailLinkappWorkInfoDTO dto);

    @Select("SELECT COUNT(1) FROM rail_linkapp_work_info WHERE tenant_id = #{tenantId} and name =#{name}")
    Integer countByOrgName(@Param("tenantId")  String tenantId,@Param("name")  String name);

    @Select("SELECT max(a.sort_index) from rail_linkapp_work_info a where a.tenant_id=#{tenantId} ")
    Integer countIndexMaxByTenantId(String tenantId);

    @Select("SELECT a.* from rail_linkapp_work_info a where a.tenant_id=#{tenantId} and a.name=#{jobName}  ")
    RailLinkappWorkInfoVO selectByJobName(@Param("tenantId")String tenantId,@Param("jobName") String jobName);
}
