package com.easylinkin.linkappapi.penetsecuremanage.utils;

import org.apache.commons.beanutils.PropertyUtils;
//import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellReference;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.*;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * excel操作新增功能
 * 
 * <AUTHOR>
 * 
 */
public class ExcelHelper {
	
	
	
	 /**
     * 导出积分报表详情数据
     */
    public static String  pointsReportExportTool(String[] header, String[] header_cn,
                                                 String[] header2, String[] header_cn2,
                                                 String[] header3, String[] header_cn3,
                                                 List<Map<String,Object>> data, Map<String,List<Map<String,Object>>> twoDate,
                                                 String fileName, HttpServletRequest request, HttpServletResponse response, Integer columnWidth){
    	OutputStream os = null;
    	boolean isOne = true;
        try {
			HSSFWorkbook wb = new HSSFWorkbook();
			HSSFFont createFont = wb.createFont();
			int pageSize = 10000;// 一万条数据一页
			HSSFSheet sheet = null;
			int size = (int) Math.ceil((double) data.size() / pageSize);
            for (int x = 0; x < size; x++) {
            	int page_num = x + 1;
				sheet = wb.createSheet("第" + page_num + "页");

				// HSSFSheet sheet = wb.createSheet();
				HSSFRow row = sheet.createRow(0);
				HSSFCellStyle style = wb.createCellStyle();
				
				HSSFCell cell_ = row.createCell(0);
				cell_.setCellValue("序号");
				cell_.setCellStyle(style);
				// 表格第一个是0行0列
				for (int i = 0; i < header_cn.length; i++) {// 加字段名
					HSSFCell cell = row.createCell(i + 1);
					sheet.setColumnWidth(i, header_cn[i].getBytes().length * columnWidth * 300);// 设置单元格宽度
					cell.setCellValue(header_cn[i]);
					cell.setCellStyle(style);
				}
				if (isOne) {
					isOne = false;
					//continue;
				}
				
				//int max = (x + 1) * pageSize >= data.size() ? data.size() : (x + 1) * pageSize;
				//Map<String, Object> map = null;
				int rowIndex = 0;
				int xh = 0;
				for(int indexMax = 0;indexMax<data.size();indexMax++){
					rowIndex++;
					row = sheet.createRow(rowIndex);
					// 添加序号
					HSSFCell cell_temp = row.createCell(0);
					cell_temp.setCellStyle(style);
					cell_temp.setCellValue(String.valueOf(xh + 1));
					xh++;
					Map<String, Object> s = null;
					for (int j = 0; j < header.length; j++) {
						// 根据属性名获取值
						s = data.get(indexMax);
						Object pro = PropertyUtils.getProperty(s, header[j]);
						if (pro == null) {
							pro = " ";
						}
							
                        	// 处理格式
                        pro = getData(header[j], pro);

						HSSFCell cell = row.createCell(j + 1);
						cell.setCellStyle(style);
						cell.setCellValue(String.valueOf(pro));
					}
					
					if (twoDate.containsKey(s.get("id"))) {
						rowIndex++;
						row = sheet.createRow(rowIndex);
						for (int i = 0; i < header_cn2.length; i++) {// 加字段名
							HSSFCell cell = row.createCell(i + 2);
							//HSSFCellStyle cellStyle = cell.getCellStyle();
							sheet.setColumnWidth(i, header_cn2[i].getBytes().length * columnWidth * 300);// 设置单元格宽度
							cell.setCellValue(header_cn2[i]);
							createFont.setBold(true);
							cell.setCellStyle(style);
							//style.setFillBackgroundColor(null);
							//style.setFillPattern(FillPatternType.THICK_BACKWARD_DIAG);
						}
						List<Map<String, Object>> list = twoDate.get(s.get("id"));
						for(int ind = 0;ind<list.size();ind++){
							rowIndex++;
							row = sheet.createRow(rowIndex);
							Map<String, Object> h = null;
							for (int j = 0; j < header_cn2.length; j++) {
								// 根据属性名获取值
								h = list.get(ind);
								Object pro = PropertyUtils.getProperty(h, header2[j]);
								if (pro == null) {
									pro = " ";
								}
									
		                        // 处理格式
		                        pro = getData(header2[j], pro);

								HSSFCell cell = row.createCell(j + 2);
								cell.setCellStyle(style);
								cell.setCellValue(String.valueOf(pro));
							}
							List<Map<String,Object>>diet =(List<Map<String,Object>>)h.get("threeArrs");
							if (diet!=null) {
								if (diet.size()>0) {
									rowIndex++;
									row = sheet.createRow(rowIndex);
									for (int i = 0; i < header_cn3.length; i++) {// 加字段名
										HSSFCell cell = row.createCell(i + 3);
										sheet.setColumnWidth(i, header_cn3[i].getBytes().length * columnWidth * 300);// 设置单元格宽度
										cell.setCellValue(header_cn3[i]);
										cell.setCellStyle(style);
									}
									for(int deInd = 0;deInd<diet.size();deInd++){
										rowIndex++;
										row = sheet.createRow(rowIndex);
										for (int z = 0; z < header_cn3.length; z++) {
											// 根据属性名获取值
											//h = list.get(ind);
											Object pro = PropertyUtils.getProperty(diet.get(deInd), header3[z]);
											if (pro == null) {
												pro = " ";
											}
												
					                        // 处理格式
					                        pro = getData(header2[z], pro);

											HSSFCell cell = row.createCell(z + 3);
											cell.setCellStyle(style);
											cell.setCellValue(String.valueOf(pro));
										}
									}
								}
							}
						}
					}
				}
			}
			// 设置打印对象
		HSSFPrintSetup printSetup = sheet.getPrintSetup();
		// 设置打印方向--横向打印
		printSetup.setLandscape(true);
		// 设置A4纸
		printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
		// 以下载的形式
		response.setContentType("application/octet-stream");

		// 根据不同浏览器 设置response的Header
		String userAgent = request.getHeader("User-Agent").toLowerCase();

		if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
			// ie浏览器
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

		} else {
			response.addHeader("Content-Disposition",
					"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
		}
		os = new BufferedOutputStream(response.getOutputStream());
		wb.write(os);
		os.flush();
		return null;
	} catch (Exception e) {
		e.printStackTrace();
		response.reset();
		return "exception";// 返回异常页面
	} finally {
		if (os != null) {
			try {
				os.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
	/**
	 * excel表格数据导出
	 * 
	 * @param header    第一行标题头（每列值对应表中字段名）
	 * @param header_cn 与header中字段一一对应的中文名
	 * @param data      要导出数据实体类数据
	 * @param fileName  保存的文件名
	 * @param request
	 * @param response
	 * @return
	 */

	public static String exportData(String[] header, String[] header_cn, @SuppressWarnings("rawtypes") List data,
                                    String fileName, HttpServletRequest request, HttpServletResponse response, Integer columnWidth) {
		OutputStream os = null;

		try {
			//HSSFWorkbook wb = new HSSFWorkbook();
			SXSSFWorkbook wb = new SXSSFWorkbook();
			int pageSize = data.size();// 一万条数据一页
			int size = (int) Math.ceil((double) data.size() / pageSize);
			//HSSFSheet sheet = null;
			SXSSFSheet sheet = null;
			for (int x = 0; x < size; x++) {
				int page_num = x + 1;
				sheet = wb.createSheet("第" + page_num + "页");
				
				// HSSFSheet sheet = wb.createSheet();
				//HSSFRow row = sheet.createRow(0);
				SXSSFRow row = sheet.createRow(0);

				CellStyle style = wb.createCellStyle();
				// style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
				// style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
				// style.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
				// style.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
				// style.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
				// style.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
				// 添加序号
				SXSSFCell cell_ = row.createCell(0);
				cell_.setCellValue("序号");
				cell_.setCellStyle(style);
				// 表格第一个是0行0列
				for (int i = 0; i < header_cn.length; i++) {// 加字段名
					SXSSFCell cell = row.createCell(i + 1);
					sheet.setColumnWidth(i, header_cn[i].getBytes().length * columnWidth * 300);// 设置单元格宽度
					cell.setCellValue(header_cn[i]);
					cell.setCellStyle(style);
				}
				// 从第0行1列开始加数据

				int max = (x + 1) * pageSize >= data.size() ? data.size() : (x + 1) * pageSize;

				for (int i = x * pageSize; i < max; i++) {
					row = sheet.createRow(i - (x * pageSize) + 1);
					// 添加序号
					SXSSFCell cell_temp = row.createCell(0);
					cell_temp.setCellStyle(style);
					cell_temp.setCellValue(String.valueOf(i + 1));
					for (int j = 0; j < header.length; j++) {
						// 根据属性名获取值

						Object pro = PropertyUtils.getProperty(data.get(i), header[j]);
						if (pro == null) {
							pro = " ";
						}
                        if (!fileName.startsWith("任务管理导出(社会面排查)")) {
							
                        	// 处理格式
                        	pro = getData(header[j], pro);
						}

						SXSSFCell cell = row.createCell(j + 1);
						cell.setCellStyle(style);
						cell.setCellValue(String.valueOf(pro));
					}
				}
			}

			// 设置打印对象
			PrintSetup printSetup = sheet.getPrintSetup();
			// 设置打印方向--横向打印
			printSetup.setLandscape(true);
			// 设置A4纸
			printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
			// 以下载的形式
			response.setContentType("application/octet-stream");

			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				response.addHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
				//response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));
			}
			os = new BufferedOutputStream(response.getOutputStream());
			wb.write(os);
			os.flush();
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			response.reset();
			return "exception";// 返回异常页面
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

	}

	public static String exportData2(String[] header, String[] header_cn, @SuppressWarnings("rawtypes") List data,
                                     String fileName, HttpServletRequest request, HttpServletResponse response) {
		OutputStream os = null;
		try {
			HSSFWorkbook wb = new HSSFWorkbook();
			HSSFSheet sheet = wb.createSheet();
			HSSFRow row = sheet.createRow(0);
			// cellStyleTitle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND); // 填充单元格
			// cellStyleTitle.setFillForegroundColor(HSSFColor.YELLOW.index);
			// cellStyleTitle.setAlignment(HSSFCellStyle.ALIGN_CENTER);// //居中显示
			HSSFCellStyle style = wb.createCellStyle();
			// style.setFillPattern(style.SOLID_FOREGROUND);
			// style.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
			// style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
			// style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
			// style.setBorderBottom(HSSFCellStyle.BORDER_THIN); // 下边框
			// style.setBorderLeft(HSSFCellStyle.BORDER_THIN);// 左边框
			// style.setBorderTop(HSSFCellStyle.BORDER_THIN);// 上边框
			// style.setBorderRight(HSSFCellStyle.BORDER_THIN);// 右边框
			style.setWrapText(true);
			// 添加序号
			HSSFCell cell_ = row.createCell(0);
			cell_.setCellValue("序号");
			cell_.setCellStyle(style);
			// 设置列宽度
			// sheet.autoSizeColumn(0, true);
			// 存储最大列宽
			// Map<Integer,Integer> maxWidth = new HashMap<Integer,Integer>();
			// 表格第一个是0行0列
			for (int i = 0; i < header_cn.length; i++) {// 加字段名
				HSSFCell cell = row.createCell(i + 1);
				cell.setCellValue(header_cn[i]);
				cell.setCellStyle(style);
				if (i == 0) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第一列宽度-序号
				} else if (i == 1) {
					sheet.setColumnWidth(i, 15 * 255);// 设置第二列宽度-问题编号
				} else if (i == 2) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第三列宽度-推进状态
				} else if (i == 3) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-问题标题
				} else if (i == 4) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-现象描述
				} else if (i == 5) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-问题类别
				} else if (i == 6) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-严重度
				} else if (i == 7) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-问题车型
				} else if (i == 8) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-造车阶段
				} else if (i == 9) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-问题影响
				} else if (i == 10) {
					sheet.setColumnWidth(i, 15 * 255);// 设置第四列宽度-零件编号
				} else if (i == 11) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-是否为再发问题
				} else if (i == 12) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-问题发生时间
				} else if (i == 13) {
					sheet.setColumnWidth(i, 30 * 255);// 设置第四列宽度-发现地点
				} else if (i == 14) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-发现部门
				} else if (i == 15) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-提出人
				} else if (i == 16) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-问题立项时间
				} else if (i == 17) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-立项部门
				} else if (i == 18) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-立项人
				} else if (i == 19) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-责任部门（一级）
				} else if (i == 20) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-责任部门（二级）
				} else if (i == 21) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-责任人
				} else if (i == 22) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-管理部门
				} else if (i == 23) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-管理者
				} else if (i == 24) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-临时措施
				} else if (i == 25) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-根本原因
				} else if (i == 26) {
					sheet.setColumnWidth(i, 40 * 255);// 设置第四列宽度-永久措施
				} else if (i == 27) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-措施验证时间
				} else if (i == 28) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-措施验证结果
				} else if (i == 29) {
					sheet.setColumnWidth(i, 10 * 255);// 设置第四列宽度-植入阶段
				} else if (i == 30) {
					sheet.setColumnWidth(i, 15 * 255);// 设置第四列宽度-开始植入日期
				} else if (i == 31) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-首台生效VIN号
				} else if (i == 32) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联NCMS问题号
				} else if (i == 33) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联NCMS问题状态
				} else if (i == 34) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联NCMS索赔状态
				} else if (i == 35) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联DIM问题号
				} else if (i == 36) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联DIM问题状态
				} else if (i == 37) {
					sheet.setColumnWidth(i, 20 * 255);// 设置第四列宽度-关联DIM问题状态
				}
			}
			// 从第0行1列开始加数据
			for (int i = 0; i < data.size(); i++) {
				row = sheet.createRow(i + 1);
				// 添加序号
				HSSFCell cell_temp = row.createCell(0);
				cell_temp.setCellStyle(style);
				cell_temp.setCellValue(String.valueOf(i + 1));
				for (int j = 0; j < header.length; j++) {
					// 根据属性名获取值
					Object pro = PropertyUtils.getProperty(data.get(i), header[j]);
					if (pro == null) {
						pro = " ";
					}
					HSSFCell cell = row.createCell(j + 1);
					cell.setCellStyle(style);
					cell.setCellValue(String.valueOf(pro));
				}
			}
			// 设置打印对象
			HSSFPrintSetup printSetup = sheet.getPrintSetup();
			// 设置打印方向--横向打印
			printSetup.setLandscape(true);
			// 设置A4纸
			printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
			// 以下载的形式
			response.setContentType("application/octet-stream");

			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				response.addHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
			}
			// 生成本地临时文件
			// File file = new File("D:\\"+fileName);
			// FileOutputStream fos = new FileOutputStream(file);
			// wb.write(fos);
			// fos.flush();
			// fos.close();
			// 下载到用户端
			// InputStream fis = new BufferedInputStream(new
			// FileInputStream(file));
			// byte[] buffer = new byte[fis.available()];
			// fis.read(buffer);
			// fis.close();
			os = new BufferedOutputStream(response.getOutputStream());
			// os.write(buffer);
			wb.write(os);
			os.flush();
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			response.reset();
			return "exception";// 返回异常页面
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

	}

	/**
	 * 主从表导出
	 * 
	 * @param header1_en  主表字段中文名
	 * @param header1_cn  主表字段名
	 * @param header2_en  从表字段中文名
	 * @param header2_cn  从表字段名
	 * @param masert_data 主表数据集合
	 * @param map         主从关联数据集合 key：关联ID value：从表数据集合
	 * @param fileName    导出的文件名
	 * @param request
	 * @param response
	 * @return
	 */
	public static String exportMasterAndSlaveData(String[] header1_en, String[] header1_cn, String[] header2_en,
			String[] header2_cn, List masert_data, Map<String, List> map, String fileName, HttpServletRequest request,
			HttpServletResponse response) {
		// 初始化输出流
		OutputStream os = null;
		try {
			// 初始化excel表格对象
			HSSFWorkbook wb = new HSSFWorkbook();
			// 创建工作表
			HSSFSheet sheet = wb.createSheet();
			// 指定工作表单元格样式
			HSSFCellStyle style = wb.createCellStyle();
			// style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
			// 导出
			String connect_id = null;// 主数据ID
			int rows = 0;
			for (int j = 0; j < masert_data.size(); j++) {
				// 第一行标题直接固定
				HSSFRow row = sheet.createRow(rows);// 从第0行开始
				for (int i = 0; i < header1_cn.length; i++) {// 加字段名
					HSSFCell cell = row.createCell(i);// 从第1列开始赋值
					if ("id".equals(header1_cn[i])) {// id不需要导出
						cell.setCellValue("序号");
					} else {
						cell.setCellValue(header1_cn[i]);
					}
					cell.setCellStyle(style);
					// 设置列宽度
					sheet.autoSizeColumn(i, true);
				}
				rows++;
				HSSFRow node_row = sheet.createRow(rows);// 从第1行开始
				// 将关联码值导出
				for (int k = 0; k < header1_en.length; k++) {
					Object pro = PropertyUtils.getProperty(masert_data.get(j), header1_en[k]);
					HSSFCell node_cell = node_row.createCell(k);
					node_cell.setCellStyle(style);
					if (pro == null) {
						pro = " ";
					}
					if ("id".equals(header1_en[k])) {
						connect_id = (String) pro;// id不导出
						node_cell.setCellValue(j + 1);
					} else {
						node_cell.setCellValue(String.valueOf(pro));
					}
				}
				// 关联码导完需要将行信息记录
				rows++;
				// 将明细信息导出
				if (StringUtils.isNotBlank(connect_id)) {
					// 明细标题
					HSSFRow row_mx = sheet.createRow(rows);// 从第0行开始
					for (int i = 0; i < header2_cn.length; i++) {// 加字段名
						HSSFCell cell = row_mx.createCell(i + 1);// 明细第一列空出来
						cell.setCellValue(header2_cn[i]);
						cell.setCellStyle(style);
						// 设置列宽度
						sheet.autoSizeColumn(i + 1, true);
					}
					rows++;
					List mx_info = map.get(connect_id);
					for (int m = 0; m < mx_info.size(); m++) {
						HSSFRow row_child = sheet.createRow(rows);
						for (int n = 0; n < header2_en.length; n++) {
							Object node_pro = PropertyUtils.getProperty(mx_info.get(m), header2_en[n]);
							if (node_pro == null) {
								node_pro = "";
							}
							HSSFCell node_cell_child = row_child.createCell(n + 1);
							node_cell_child.setCellStyle(style);
							node_cell_child.setCellValue(String.valueOf(node_pro));
						}
						// 每次添加完毕，行信息累加1
						rows++;
					}
					// 关联码使用完毕进行初始化
					connect_id = null;
				}

			}
			// 以下载的形式
			response.setContentType("application/octet-stream");

			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				response.addHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
			}
			os = new BufferedOutputStream(response.getOutputStream());
			wb.write(os);
			os.flush();
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			response.reset();
			return "exception";// 返回异常页面
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * poi解析excel
	 * 
	 * @param is 文件流
	 * @return 包含每一行的每一格信息字符串集合的信息集合
	 */
	public static List<List<String>> analysisExcel(InputStream is) throws InvalidFormatException {
		List<List<String>> list = new ArrayList<List<String>>();
		try {
			Workbook wb = WorkbookFactory.create(is);
			Sheet sheet = wb.getSheetAt(0);// 获取第1个副本
			DataFormatter formatter = new DataFormatter();// 格式化获取的单元格值
			for (Row row : sheet) { // 遍历当前副本每一行
				List<String> temp = new ArrayList<String>();
				for (Cell cell : row) {// 遍历当前行每一格
					temp.add(formatter.formatCellValue(cell));
				}
				list.add(temp);
			}
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		return list;
	}

	/**
	 * poi解析excel包含必输项和非必输项（根据列取值）
	 * 
	 * @param is    文件流
	 * @param index 列的最大序号值
	 * @return 包含每一行的每一格信息字符串集合的信息集合
	 */
	public static List<List<String>> analysisExcel(InputStream is, int index) throws InvalidFormatException {
		List<List<String>> list = new ArrayList<List<String>>();
		try {
			Workbook wb = WorkbookFactory.create(is);
			Sheet sheet = wb.getSheetAt(0);// 获取第1个副本
			DataFormatter formatter = new DataFormatter();// 格式化获取的单元格值
			for (Row row : sheet) { // 遍历当前副本每一行
				List<String> temp = new ArrayList<String>();
				// 先获取非必输项是否有值，如果无值则补空白值
				for (int i = 0; i < index; i++) {// 遍历当前行想取值的每一格
					Cell temp_value = row.getCell(i);
					temp.add(formatter.formatCellValue(temp_value));
				}
				list.add(temp);
			}
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		return list;
	}

	/**
	 * poi解析excel包含必输项和非必输项（根据行、列取值）
	 * 
	 * @param is        文件流
	 * @param row_index 行的最大值
	 * @param index     列的最大序号值
	 * @return 包含每一行的每一格信息字符串集合的信息集合
	 */
	public static List<List<String>> analysisExcel(InputStream is, int row_index, int index)
			throws InvalidFormatException {
		List<List<String>> list = new ArrayList<List<String>>();
		try {
			Workbook wb = WorkbookFactory.create(is);
			Sheet sheet = wb.getSheetAt(0);// 获取第1个副本
			DataFormatter formatter = new DataFormatter();// 格式化获取的单元格值
			for (int num = 0; num < row_index; num++) { // 遍历当前副本每一行
				List<String> temp = new ArrayList<String>();
				Row row = sheet.getRow(num);// 根据行号取行
				if (row != null) {
					// 先获取非必输项是否有值，如果无值则补空白值
					for (int i = 0; i < index; i++) {// 遍历当前行想取值的每一格
						Cell temp_value = row.getCell(i);
						temp.add(formatter.formatCellValue(temp_value));
					}
					list.add(temp);
				}
			}
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		return list;
	}

	/**
	 * excel表格数据导出2007版
	 * 
	 * @param header    第一行标题头（每列值对应表中字段名）
	 * @param header_cn 与header中字段一一对应的中文名
	 * @param data      要导出数据实体类数据
	 * @param fileName  保存的文件名
	 * @param request
	 * @param response
	 * @return
	 */
	public static String exportNewData(String[] header, String[] header_cn, @SuppressWarnings("rawtypes") List data,
                                       String fileName, HttpServletRequest request, HttpServletResponse response) {
		OutputStream os = null;
		try {
			XSSFWorkbook wb = new XSSFWorkbook();
			XSSFSheet sheet = wb.createSheet();
			XSSFRow row = sheet.createRow(0);
			// cellStyleTitle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND); // 填充单元格
			// cellStyleTitle.setFillForegroundColor(HSSFColor.YELLOW.index);
			// cellStyleTitle.setAlignment(HSSFCellStyle.ALIGN_CENTER);// //居中显示
			XSSFCellStyle style = wb.createCellStyle();
			// style.setFillPattern(style.SOLID_FOREGROUND);
			// style.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
			// style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
			// 添加序号
			XSSFCell cell_ = row.createCell(0);
			cell_.setCellValue("序号");
			cell_.setCellStyle(style);
			// 设置列宽度
			sheet.autoSizeColumn(0, true);
			// 表格第一个是0行0列
			for (int i = 0; i < header_cn.length; i++) {// 加字段名
				XSSFCell cell = row.createCell(i + 1);
				cell.setCellValue(header_cn[i]);
				cell.setCellStyle(style);
				// 设置列宽度
				sheet.autoSizeColumn(i + 1, true);
			}
			// 从第0行1列开始加数据
			for (int i = 0; i < data.size(); i++) {
				row = sheet.createRow(i + 1);
				// 添加序号
				XSSFCell cell_temp = row.createCell(0);
				cell_temp.setCellStyle(style);
				cell_temp.setCellValue(String.valueOf(i + 1));
				for (int j = 0; j < header.length; j++) {
					// 根据属性名获取值
					Object pro = PropertyUtils.getProperty(data.get(i), header[j]);
					if (pro == null) {
						pro = " ";
					}
					XSSFCell cell = row.createCell(j + 1);
					cell.setCellStyle(style);
					cell.setCellValue(String.valueOf(pro));

				}
			}
			// 以下载的形式
			response.setContentType("application/octet-stream");

			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				response.addHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
			}
			// 生成本地临时文件
			// File file = new File("D:\\"+fileName);
			// FileOutputStream fos = new FileOutputStream(file);
			// wb.write(fos);
			// fos.flush();
			// fos.close();
			// 下载到用户端
			// InputStream fis = new BufferedInputStream(new
			// FileInputStream(file));
			// byte[] buffer = new byte[fis.available()];
			// fis.read(buffer);
			// fis.close();
			os = new BufferedOutputStream(response.getOutputStream());
			// os.write(buffer);
			wb.write(os);
			os.flush();
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			response.reset();
			return "exception";// 返回异常页面
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * poi解析excel包含必输项和非必输项（根据列取值）
	 * 
	 * @param is 文件流
	 * @return
	 */
	public static List<List<String>> analysisExcelCell(InputStream is) throws InvalidFormatException {
		List<List<String>> list = new ArrayList<List<String>>();
		try {
			Workbook wb = WorkbookFactory.create(is);
			Sheet sheet = wb.getSheetAt(0);// 获取第1个副本
			DataFormatter formatter = new DataFormatter();// 格式化获取的单元格值
			for (Row row : sheet) { // 遍历当前副本每一行
				List<String> temp = new ArrayList<String>();
				// 获取有多少列
				int cellSize = row.getLastCellNum();
				// 先获取非必输项是否有值，如果无值则补空白值
				for (int i = 0; i < cellSize; i++) {// 遍历当前行想取值的每一格
					Cell temp_value = row.getCell(i);
					temp.add(formatter.formatCellValue(temp_value));
				}
				list.add(temp);
			}
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		return list;
	}

	private static String getLevel(String level) {

		String text = "";

		switch (level) {
		case "1":
			text = "I级";
			break;
		case "2":
			text = "II级";
			break;
		case "3":
			text = "III级";
			break;
		default:
			break;
		}

		return text;
	}



	private static Object getData(String header, Object pro) {
		// 处理人员档案导出的风险等级
		if (header == "level") {
			pro = StringUtils.isEmpty(String.valueOf(pro)) || pro == " " ? ""
					: getLevel(String.valueOf(pro));
		}
		return pro;
	}



	private static Object getData3(String header, Object pro) {
		if (header == "realName") {
			pro = String.valueOf(pro);
		}
		if (header == "sex") {
			pro = String.valueOf(pro) ;
		}
		if (header == "birthday") {
			pro = String.valueOf(pro);
		}
		if (header == "idsNo") {
			pro = String.valueOf(pro);
		}
		if (header == "verificationNo") {
			pro = String.valueOf(pro);
		}
		if (header == "testItem") {
			pro = String.valueOf(pro);
		}
		if (header == "testResult") {
			pro = String.valueOf(pro)  ;
		}
		if (header == "verificationOrgName") {
			pro = String.valueOf(pro);
		}
		if (header == "testTime") {
			pro = String.valueOf(pro) ;
		}
		if (header == "testAddress") {
			pro = String.valueOf(pro);
		}

		return pro;
	}
public static String readCell(Cell cell, int row, int column) {
		
		String result="";
		// ## 获取CellReference对象 ##/ 
        CellReference cellRef = new CellReference(row, column);
        System.out.print(cellRef.formatAsString()); 
        System.out.print(" - "); 
        // ## 根据CELL值类型进行相应处理 ##/ 
        switch (cell.getCellType()) { 
        case STRING: 
            
            result=cell.getRichStringCellValue() 
                    .getString();
            break; 
        case NUMERIC: 
            // ## yyyy年mm月dd日 hh:mm:ss此种格式日期不能识别 ##// 
            // ## mm/dd/yyyy h:mm,yyyy-MM-dd 
            // hh:mm:ss可以识别,估计是POI对中文日期支持不怎么好的问题 ##// 
            if (DateUtil.isCellDateFormatted(cell)) {

                result=String.valueOf(cell.getDateCellValue());
            } else { 
                
                result=String.valueOf(cell.getNumericCellValue());
            } 
            break; 
        case BOOLEAN: 
            
            result=String.valueOf(cell.getBooleanCellValue());
            break; 
        case FORMULA: 
            result=cell.getCellFormula();
            break; 
        case ERROR: 
            result=String.valueOf(cell.getErrorCellValue());
            break; 
        default: 
        	System.out.print("nothing"); 
        } 
        System.out.print(" row:"+row+" column:"+column+" result:"+result+" ref:"+cellRef.formatAsString());
        
        return result;
	}


    public static String Multipleworkbooks(String[] header_cn, String[] header, HttpServletRequest request, HttpServletResponse response, String fileName, Map<String, List<Map<String, Object>>> transForMap,List<Map<String,Object>> bookNames, int columnWidth,String valueName,String keyId) {

		OutputStream os = null;

		try {
			//HSSFWorkbook wb = new HSSFWorkbook();
			SXSSFWorkbook wb = new SXSSFWorkbook();
			int pageSize = bookNames.size();// 一万条数据一页

			//HSSFSheet sheet = null;
			SXSSFSheet sheet = null;
			for (int x = 0; x < pageSize; x++) {
				Map<String, Object> nameMap = bookNames.get(x);
				String workName = Objects.toString(nameMap.get(valueName),"");
				String workId = Objects.toString(nameMap.get(keyId),"");
				sheet = wb.createSheet(workName);
				SXSSFRow row = sheet.createRow(0);

				CellStyle style = wb.createCellStyle();
				// 添加序号
				SXSSFCell cell_ = row.createCell(0);
				cell_.setCellValue("序号");
				cell_.setCellStyle(style);
				for (int i = 0; i < header_cn.length; i++) {// 加字段名
					SXSSFCell cell = row.createCell(i + 1);
					sheet.setColumnWidth(i, header_cn[i].getBytes().length * columnWidth * 300);// 设置单元格宽度
					cell.setCellValue(header_cn[i]);
					cell.setCellStyle(style);
				}
				List<Map<String, Object>> data = transForMap.get(workId);
				if (data.size() ==  0){
                      continue;
				}
				int max = data.size();
				for (int i = 0; i < max; i++) {
					row = sheet.createRow(i+1);
					// 添加序号
					SXSSFCell cell_temp = row.createCell(0);
					cell_temp.setCellStyle(style);
					cell_temp.setCellValue(String.valueOf(i+1));
					for (int j = 0; j < header.length; j++) {
						// 根据属性名获取值

						Object pro = PropertyUtils.getProperty(data.get(i), header[j]);
						if (pro == null) {
							pro = " ";
						}
						// 处理格式
						pro = getData(header[j], pro);
						SXSSFCell cell = row.createCell(j + 1);
						cell.setCellStyle(style);
						cell.setCellValue(String.valueOf(pro));
					}
				}
			}
			// 设置打印对象
			PrintSetup printSetup = sheet.getPrintSetup();
			// 设置打印方向--横向打印
			printSetup.setLandscape(true);
			// 设置A4纸
			printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
			// 以下载的形式
			response.setContentType("application/octet-stream");

			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				response.addHeader("Content-Disposition",
						"attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
				//response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));
			}
			os = new BufferedOutputStream(response.getOutputStream());
			wb.write(os);
			os.flush();
			return null;
		}catch (Exception e){
			e.printStackTrace();
			response.reset();
			return "exception";// 返回异常页面
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}



	public static void exportGridInfoToExcel(List<GridInfo> data, String fileName,HttpServletRequest request, HttpServletResponse response) {
		try {
			HSSFWorkbook workbook = new HSSFWorkbook();
			//orkbook.setCompressTempFiles(true);
			OutputStream os = null;
			HSSFSheet sheet = workbook.createSheet("网格信息");
			// 创建标题样式
			HSSFCellStyle headerStyle = createHeaderStyle(workbook);
			// 创建数据行样式
			HSSFCellStyle dataStyle = createDataStyle(workbook);
			// 创建标题行
			createHeaderRow(sheet, headerStyle);
			// 填充数据
			fillDataRows(sheet, dataStyle, data);
			// 自动调整列宽
			//autoSizeColumns(sheet);
			// 设置打印对象
			// 设置打印对象
			// 设置打印对象
			HSSFPrintSetup printSetup = sheet.getPrintSetup();
			// 设置打印方向--横向打印
			printSetup.setLandscape(true);
			// 设置A4纸
			printSetup.setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
			// 以下载的形式

			// 1. 关键设置：重置响应并禁用字符编码
			response.reset();  // 清除所有已有设置
			response.setCharacterEncoding(null); // 禁用字符编码器

			// 2. 设置二进制响应头
			response.setContentType("application/vnd.ms-excel");
			//response.setContentType("application/vnd.ms-excel");
			response.setHeader("Cache-Control", "no-transform"); // 关键：禁止中间层修改
			response.setHeader("X-Content-Type-Options", "nosniff"); // 禁止浏览器MIME嗅探
			// 根据不同浏览器 设置response的Header
			String userAgent = request.getHeader("User-Agent").toLowerCase();

			if (userAgent.indexOf("msie") > 0 || (userAgent.indexOf("gecko") > 0 && userAgent.indexOf("rv:11") > 0)) {
				// ie浏览器
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));

			} else {
				 fileName = URLEncoder.encode("中文报表.xls", StandardCharsets.UTF_8.name());
				response.addHeader("Content-Disposition",
						"attachment; filename*=UTF-8''" + fileName);
				//response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));
			}
			os = new BufferedOutputStream(response.getOutputStream());
			workbook.write(os);
			os.flush();
			os.close();
			//workbook.dispose();
			workbook.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private static HSSFCellStyle createHeaderStyle(HSSFWorkbook workbook) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBold(true);
		font.setFontHeightInPoints((short) 12);
		style.setFont(font);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		return style;
	}

	private static HSSFCellStyle createDataStyle(HSSFWorkbook workbook) {
		HSSFCellStyle style = workbook.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setWrapText(true);
		return style;
	}

	private static void createHeaderRow(HSSFSheet sheet, HSSFCellStyle style) {
		HSSFRow headerRow = sheet.createRow(0);

		// 合并第一行的前几个单元格作为大标题
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 17));

		HSSFCell titleCell = headerRow.createCell(0);
		titleCell.setCellValue("项目网格划分及安监专务信息表");
		titleCell.setCellStyle(style);

		// 创建第二行表头
		HSSFRow subHeaderRow = sheet.createRow(1);
		String[] headers = {
				"网格编号", "网格类别", "工程类别", "网格名称", "网格里程范围",
				"安监专务", "网格安全员及施工员信息", "","","", "施工作业队伍信息", "",
				"高峰期作业人员数量", "施工队伍", "网格安全员是否是专职","持证情况",
				"网格安全员是否正式职工", "备注"
		};

		for (int i = 0; i < headers.length; i++) {
			HSSFCell cell = subHeaderRow.createCell(i);
			cell.setCellValue(headers[i]);
			cell.setCellStyle(style);
		}

		// 创建第三行表头
		HSSFRow detailHeaderRow = sheet.createRow(2);
		String[] detailHeaders = {
				"", "", "", "", "", "",
				"网格安全员", "联系方式", "施工员/领工员", "联系方式",
				"现场负责人/安全员", "联系方式", "", "", "","",""
		};

		for (int i = 0; i < detailHeaders.length; i++) {
			HSSFCell cell = detailHeaderRow.createCell(i);
			cell.setCellValue(detailHeaders[i]);
			cell.setCellStyle(style);
		}
		// 合并表头单元格
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0)); // 网格编号
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1)); // 网格类别
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2)); // 工程类别
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3)); // 具体网格
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4)); // 网格里程范围
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5)); // 安监专务
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 6, 9)); // 网格安全员及施工员信息

	/*	sheet.addMergedRegion(new CellRangeAddress(1, 2, 6, 6)); // 网格安全员及施工员信息
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 7, 7)); // 网格安全员及施工员信息
		sheet.addMergedRegion(new CellRangeAddress(1,2, 8, 8)); // 网格安全员及施工员信息
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 9, 9)); // 网格安全员及施工员信息*/

		sheet.addMergedRegion(new CellRangeAddress(1, 1, 10, 11)); // 施工作业队伍信息

		/*sheet.addMergedRegion(new CellRangeAddress(1, 2, 10, 10)); // 施工作业队伍信息
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 11, 11)); // 施工作业队伍信息*/


		sheet.addMergedRegion(new CellRangeAddress(1, 2, 12, 12)); // 高峰期作业人员数量
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 13, 13)); // 施工队伍
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 14, 14)); // 网格安全员是否是专职
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 15, 15)); // 网格安全员是否是专职

		sheet.addMergedRegion(new CellRangeAddress(1, 2,16, 16));  // 网格安全员是否正式职工
		sheet.addMergedRegion(new CellRangeAddress(1, 2, 17, 17)); // 备注
	}

	/*private static void fillDataRows(Sheet sheet, CellStyle style, List<GridInfo> gridInfoList) {
		int rowNum = 3; // 数据从第4行开始

		for (GridInfo gridInfo : gridInfoList) {
			Row row = sheet.createRow(rowNum++);

			createCell(row, 0, gridInfo.getGridNumber(), style);
			createCell(row, 1, gridInfo.getGridType(), style);
			createCell(row, 2, gridInfo.getProjectType(), style);
			createCell(row, 3, gridInfo.getSpecificGrid(), style);
			createCell(row, 4, gridInfo.getGridRange(), style);
			createCell(row, 5, gridInfo.getSafetySupervisor(), style);
			createCell(row, 6, gridInfo.getGridSafetyOfficer(), style);
			createCell(row, 7, gridInfo.getSafetyOfficerContact(), style);
			createCell(row, 8, gridInfo.getConstructionSupervisor(), style);
			createCell(row, 9, gridInfo.getSupervisorContact(), style);
			createCell(row, 10, gridInfo.getSiteManager(), style);
			createCell(row, 11, gridInfo.getPeakWorkers(), style);
			createCell(row, 12, gridInfo.getConstructionTeam(), style);
			createCell(row, 13, gridInfo.getIsFullTime(), style);
			createCell(row, 14, gridInfo.getIsFormalEmployee(), style);
			createCell(row, 15, gridInfo.getRemarks(), style);
		}*/


	private static void createCell(HSSFRow row, int column, String value, HSSFCellStyle style) {
		HSSFCell cell = row.createCell(column);
		cell.setCellValue(value);
		cell.setCellStyle(style);
	}
	private static void fillDataRows(HSSFSheet sheet, HSSFCellStyle style, List<GridInfo> gridInfoList) {
		int rowNum = 3; // 数据从第4行开始

		for (GridInfo gridInfo : gridInfoList) {
			HSSFRow  row = sheet.createRow(rowNum++);
			createCell(row, 0,Objects.toString( gridInfo.getNumber(),"0"), style);
			createCell(row, 1, gridInfo.getGridType(), style);
			createCell(row, 2, gridInfo.getEngineeType(), style);
			createCell(row, 3, gridInfo.getGridName(), style);
			createCell(row, 4, gridInfo.getMileageRange(), style);
			createCell(row, 5, gridInfo.getSafetySupName(), style);

			createCell(row, 6, gridInfo.getGridSecName(), style);
			createCell(row, 7, gridInfo.getGridSecPhone(), style);
			createCell(row, 8, gridInfo.getGridForemanName(), style);
			createCell(row, 9, gridInfo.getGridForemanPhone(), style);
			createCell(row, 10, gridInfo.getGridSitemanName(), style);
			createCell(row, 11, gridInfo.getGridSitemanPhone(), style);

			createCell(row, 12, Objects.toString(gridInfo.getStaffNumber(),""), style);
			createCell(row, 13, gridInfo.getConstructionTeam(), style);
			createCell(row, 14, gridInfo.getIsSoleDuty(), style);
			createCell(row, 15, gridInfo.getHoldCert(), style);
			createCell(row, 16, gridInfo.getIsFormalEmployee(), style);
			createCell(row, 17,gridInfo.getNotes(), style);

		}

	}

}
