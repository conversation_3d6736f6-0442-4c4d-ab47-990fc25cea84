package com.easylinkin.linkappapi.penetsecuremanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.easylinkin.linkappapi.penetsecuremanage.dto.RailLinkappGridManagementInfoDTO;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import com.easylinkin.linkappapi.penetsecuremanage.vo.RailLinkappGridManagementInfoVO;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface RailLinkappGridManagementInfoMapper extends BaseMapper<RailLinkappGridManagementInfo>  {

    IPage<RailLinkappGridManagementInfoVO> selectPage(Page<RailLinkappGridManagementInfo> page, @Param("entity") RailLinkappGridManagementInfoDTO railLinkappGridManagementInfoDTO);

    List<RailLinkappGridManagementInfoVO> getList(@Param("entity") RailLinkappGridManagementInfoDTO dto);

    @Select("SELECT max(a.number) from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} ")
    Integer countIndexMaxNumber(@Param("tenantId")String tenantId);

    @Select("SELECT count(1) from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} and a.grid_name =#{gridName}")
    Integer countByGridName(@Param("tenantId")String tenantId,@Param("gridName")String gridName);

    @Select("SELECT count(1) from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} and (a.grid_sec_id=#{safetySupId} or a.grid_foreman_id=#{safetySupId} or a.grid_siteman_id=#{safetySupId} or a.grid_meet_id=#{safetySupId} )")
    Integer checkAjzwByNumber(@Param("tenantId") String tenantId, @Param("safetySupId")String safetySupId);

    @Select("SELECT count(1) from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} and a.safety_sup_id=#{psId} ")
    Integer checkOtherByNumber(@Param("tenantId") String tenantId, @Param("psId")String psId);

    @Select("SELECT * from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} ")
    List<RailLinkappGridManagementInfo> queryByTenantId(String tenantId);


    @Select("SELECT a.* from rail_linkapp_grid_management_info a where  a.tenant_id=#{tenantId} and (a.grid_sec_id=#{pesId} or a.grid_foreman_id=#{pesId} or a.grid_siteman_id=#{pesId} or a.safety_sup_id=#{pesId} or a.grid_meet_id=#{pesId} )")
    List<RailLinkappGridManagementInfo> selectByPes(@Param("tenantId") String tenantId, @Param("pesId")String pesId);

    void delByAnnouncementFileUrl(@Param("id") String id);
}
